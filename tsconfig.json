{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "noEmit": true, "incremental": true, "tsBuildInfoFile": ".tsbuildinfo", "resolveJsonModule": true, "isolatedModules": true}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", ".expo/types/**/*.ts", "expo-env.d.ts"], "exclude": ["node_modules", "babel.config.js", "metro.config.js"]}