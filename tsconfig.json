{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "noImplicitOverride": true, "allowUnusedLabels": false, "allowUnreachableCode": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "jsx": "react-native", "lib": ["dom", "esnext"], "moduleDetection": "force", "resolveJsonModule": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/components/*": ["src/components/*"], "@/services/*": ["src/services/*"], "@/context/*": ["src/context/*"], "@/utils/*": ["src/utils/*"], "@/types/*": ["src/types/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}