import React, { useState, useRef, useCallback, useMemo } from 'react';
import { FlatList, ScrollView, View, StyleSheet, Alert, TouchableOpacity, Image, RefreshControl } from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  Searchbar,
  Chip,
  Surface,
  Divider,
  IconButton,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import UnifiedSearch from '../components/UnifiedSearch';
import navigationService from '../services/NavigationService';
// OrderBottomSheet removed - using dedicated AddOrder page
import OrderDetailsBottomSheet from '../components/OrderDetailsBottomSheet';
import PDFInvoiceBottomSheet from '../components/PDFInvoiceBottomSheet';
import { PDFInvoiceGenerator } from '../utils/pdfInvoiceGenerator';
import CommonHeader from '../components/CommonHeader';
import UnifiedFilterChips from '../components/UnifiedFilterChips';
import UnifiedEmptyState from '../components/UnifiedEmptyState';
import UnifiedInfoCard from '../components/UnifiedInfoCard';

const OrdersScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  // orderBottomSheetRef removed - using dedicated AddOrder page
  const orderDetailsBottomSheetRef = useRef(null);
  const invoiceBottomSheetRef = useRef(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [invoiceOrder, setInvoiceOrder] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  const statuses = ['All', 'Pending', 'In Progress', 'Completed', 'Cancelled'];

  // Pull to refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Simulate data refresh - in real app, this would reload from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Orders data refreshed');
    } catch (error) {
      console.log('Failed to refresh orders data:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  const filteredOrders = useMemo(() => {
    return state.orders.filter(order => {
      // Safely handle customer name with multiple fallbacks
      const customerName = order?.customerName || order?.customer || '';
      const orderId = order?.id ? order.id.toString() : '';
      const orderStatus = order?.status || 'pending';

      // Safely perform search matching
      const matchesSearch = (customerName && customerName.toLowerCase().includes(searchQuery.toLowerCase())) ||
                           (orderId && orderId.includes(searchQuery));
      const matchesStatus = selectedStatus === 'All' || orderStatus === selectedStatus;
      return matchesSearch && matchesStatus;
    });
  }, [state.orders, searchQuery, selectedStatus]);

  const handleAddOrder = () => {
    console.log('Navigating to Add Order page...');
    try {
      navigationService.navigate('AddOrder');
    } catch (error) {
      console.error('Failed to navigate to AddOrder:', error);
    }
  };

  const handleOrderPress = useCallback((order) => {
    console.log('Order card pressed:', order.id);
    setSelectedOrder(order);
    // Add a small delay to ensure state is set before opening bottom sheet
    setTimeout(() => {
      orderDetailsBottomSheetRef.current?.expand();
    }, 50);
  }, []);

  const handleEditOrder = useCallback((order) => {
    console.log('Navigating to Edit Order page...', order.id);
    try {
      // Navigate to AddOrder page with order data for editing
      navigationService.navigate('AddOrder', { order });
    } catch (error) {
      console.error('Failed to navigate to edit order:', error);
    }
  }, []);

  const handleDeleteOrder = useCallback((order) => {
    Alert.alert(
      'Delete Order',
      `Are you sure you want to delete order #${order.id}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => actions.deleteOrder(order.id),
        },
      ]
    );
  }, [actions]);

  const handleUpdateOrderStatus = useCallback((orderId, newStatus) => {
    actions.updateOrderStatus(orderId, newStatus);
  }, [actions]);

  const handleViewInvoice = useCallback((order) => {
    console.log('Opening invoice for order:', order.id);
    setInvoiceOrder(order);
    invoiceBottomSheetRef.current?.expand();
  }, []);

  const handleGeneratePDF = async (order) => {
    try {
      const pdf = await PDFInvoiceGenerator.generatePDF(order);
      Alert.alert(
        'PDF Generated',
        `Invoice PDF saved successfully!\nLocation: ${pdf.filePath}`,
        [
          { text: 'OK' },
          {
            text: 'Share',
            onPress: async () => {
              try {
                await PDFInvoiceGenerator.generateAndSharePDF(order);
              } catch (error) {
                Alert.alert('Share Error', 'Failed to share PDF invoice.');
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error('PDF generation error:', error);
      Alert.alert('PDF Error', 'Failed to generate PDF invoice. Please try again.');
    }
  };

  // handleSaveOrder removed - using dedicated AddOrder page

  const getStatusColor = useCallback((status) => {
    switch (status) {
      case 'Completed':
        return {
          bg: theme.colors.tertiary, // Green - Success
          text: theme.colors.onTertiary,
        };
      case 'In Progress':
        return {
          bg: theme.colors.secondary, // Orange - In Progress
          text: theme.colors.onSecondary,
        };
      case 'Pending':
        return {
          bg: theme.colors.primary, // Blue - Waiting
          text: theme.colors.onPrimary,
        };
      case 'Cancelled':
        return {
          bg: theme.colors.error, // Red - Error/Cancelled
          text: theme.colors.onError,
        };
      default:
        return {
          bg: theme.colors.surfaceVariant,
          text: theme.colors.onSurfaceVariant,
        };
    }
  }, [theme]);

  const renderOrderCard = useCallback(({ item: order }) => {
    const statusColors = getStatusColor(order.status);

    return (
      <TouchableOpacity
        onPress={() => handleOrderPress(order)}
        activeOpacity={0.7}
        delayPressIn={0}
        delayPressOut={100}
      >
        <Surface style={[styles.orderCard, { backgroundColor: theme.colors.surface }]} elevation={0}>
          <View style={styles.orderContent}>
            <View style={styles.orderHeader}>
              {order.image && (
                <Image source={{ uri: order.image }} style={styles.orderImageCompact} />
              )}
              <View style={styles.orderInfo}>
                <View style={styles.orderTitleRow}>
                  <Text variant="titleMedium" style={{ fontWeight: '600', flex: 1 }}>#{order.id}</Text>
                  <Text variant="titleLarge" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                    ৳{order.total.toFixed(2)}
                  </Text>
                </View>
                <View style={styles.orderMetaRow}>
                  <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, flex: 1 }}>
                    {order.date} • {order.customerName || order.customer || 'Unknown Customer'}
                  </Text>
                  <View style={[styles.statusBadge, { backgroundColor: statusColors.bg }]}>
                    <Text variant="bodySmall" style={{ color: statusColors.text, fontWeight: '600' }}>
                      {order.status}
                    </Text>
                  </View>
                </View>
              </View>
            </View>

            <View style={styles.itemsSection}>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                {order.items.length} item{order.items.length !== 1 ? 's' : ''} • {order.customerPhone || order.phone || 'No phone'}
              </Text>
            </View>

            <View style={styles.actionButtons}>
              <View style={styles.actionButtonsLeft}>
                <IconButton
                  icon="pencil-outline"
                  size={18}
                  iconColor={theme.colors.onSurfaceVariant}
                  onPress={() => handleEditOrder(order)}
                  style={styles.editButton}
                />
                {order.status === 'Pending' && (
                  <>
                    <Button
                      mode="outlined"
                      onPress={() => handleUpdateOrderStatus(order.id, 'Cancelled')}
                      compact
                      style={styles.actionButton}
                      icon="close-outline"
                    >
                      Cancel
                    </Button>
                    <Button
                      mode="contained"
                      onPress={() => handleUpdateOrderStatus(order.id, 'In Progress')}
                      compact
                      style={styles.primaryActionButton}
                      icon="play-outline"
                    >
                      Start
                    </Button>
                  </>
                )}
                {order.status === 'In Progress' && (
                  <Button
                    mode="contained"
                    onPress={() => handleUpdateOrderStatus(order.id, 'Completed')}
                    compact
                    style={styles.primaryActionButton}
                    icon="check-outline"
                  >
                    Complete
                  </Button>
                )}
                {order.status === 'Completed' && (
                  <Button
                    mode="contained"
                    onPress={() => handleViewInvoice(order)}
                    compact
                    style={styles.primaryActionButton}
                    icon="receipt"
                    buttonColor={theme.colors.tertiary}
                    textColor={theme.colors.onTertiary}
                  >
                    Invoice
                  </Button>
                )}
                {order.status === 'Cancelled' && (
                  <Button
                    mode="outlined"
                    onPress={() => handleUpdateOrderStatus(order.id, 'Pending')}
                    compact
                    style={styles.actionButton}
                    icon="refresh-outline"
                  >
                    Reactivate
                  </Button>
                )}
              </View>

              <IconButton
                icon="delete-outline"
                size={18}
                iconColor={theme.colors.error}
                onPress={() => handleDeleteOrder(order)}
                style={styles.deleteButton}
              />
            </View>
        </View>
      </Surface>
      </TouchableOpacity>
    );
  }, [theme, handleOrderPress, handleEditOrder, handleDeleteOrder, handleViewInvoice, handleUpdateOrderStatus, getStatusColor]);

  const orderStats = useMemo(() => ({
    total: filteredOrders.length,
    pending: filteredOrders.filter(o => o.status === 'Pending').length,
    inProgress: filteredOrders.filter(o => o.status === 'In Progress').length,
    completed: filteredOrders.filter(o => o.status === 'Completed').length,
    revenue: filteredOrders.reduce((sum, order) => sum + order.total, 0),
  }), [filteredOrders]);



  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Orders"
        subtitle="Track and manage orders"
        searchPlaceholder="Search orders..."
        searchType="orders"
        searchData={state.orders}
        searchFields={["customerName", "customer", "id", "status"]}
        onSearchChange={setSearchQuery}
        onSearchResult={(order) => {
          console.log('Order selected from search:', order);
          setSelectedOrder(order);
          orderDetailsBottomSheetRef.current?.expand();
        }}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          console.log('Profile pressed - navigating to MyProfile');
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <FlatList
        data={filteredOrders}
        renderItem={renderOrderCard}
        keyExtractor={(item) => item.id.toString()}
        style={styles.content}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        initialNumToRender={8}
        windowSize={10}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListHeaderComponent={() => (
          <View>
            {/* Status Filter */}
            <UnifiedFilterChips
              filters={statuses}
              selectedFilter={selectedStatus}
              onFilterChange={setSelectedStatus}
              showCounts={true}
              data={state.orders}
              countField="status"
              style={{ marginBottom: 16 }}
            />

            {/* Stats Row */}
            <View style={styles.statsRow}>
              <View style={styles.statCardWrapper}>
                <UnifiedInfoCard
                  type="stat"
                  icon="clipboard-list-outline"
                  iconColor={theme.colors.primary}
                  value={orderStats.total.toString()}
                  title="Total Orders"
                  elevation={1}
                />
              </View>
              <View style={styles.statCardWrapper}>
                <UnifiedInfoCard
                  type="stat"
                  icon="currency-usd"
                  iconColor={theme.colors.secondary}
                  value={`৳${orderStats.revenue.toFixed(0)}`}
                  title="Revenue"
                  elevation={1}
                />
              </View>
            </View>
          </View>
        )}
        ListEmptyComponent={() => (
          <UnifiedEmptyState
            type="orders"
            searchQuery={searchQuery}
            onActionPress={handleAddOrder}
          />
        )}
      />



      {/* OrderBottomSheet removed - using dedicated AddOrder page */}

      <OrderDetailsBottomSheet
        bottomSheetRef={orderDetailsBottomSheetRef}
        order={selectedOrder}
        onUpdateStatus={handleUpdateOrderStatus}
        onEdit={handleEditOrder}
        onDelete={handleDeleteOrder}
      />

      <PDFInvoiceBottomSheet
        ref={invoiceBottomSheetRef}
        order={invoiceOrder}
        onClose={() => setInvoiceOrder(null)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchbar: {
    flex: 1,
    borderRadius: 25,
    height: 48,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statusContainer: {
    marginBottom: 8,
  },
  statusChip: {
    marginRight: 6,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
    gap: 12,
  },
  statCardWrapper: {
    flex: 1,
  },
  orderCard: {
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    overflow: 'hidden',
  },
  orderImageCompact: {
    width: 60,
    height: 60,
    borderRadius: 8,
    resizeMode: 'cover',
    marginRight: 12,
  },
  orderContent: {
    padding: 12,
  },
  orderHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  orderInfo: {
    flex: 1,
  },
  orderTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  orderMetaRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 4,
    minWidth: 60,
    alignItems: 'center',
  },
  itemsSection: {
    marginBottom: 12,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  actionButtonsLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  editButton: {
    margin: 0,
    marginRight: 4,
  },
  deleteButton: {
    margin: 0,
  },
  actionButton: {
    minWidth: 70,
    height: 32,
  },
  primaryActionButton: {
    minWidth: 80,
    height: 32,
  },
});

export default OrdersScreen;
