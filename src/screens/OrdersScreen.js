import React, { useState, useRef, useCallback, useMemo } from 'react';
import { FlatList, ScrollView, View, StyleSheet, Alert, TouchableOpacity, Image } from 'react-native';
import {
  Card,
  Title,
  Text,
  Button,
  Searchbar,
  Chip,
  Surface,
  Divider,
  IconButton,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import OrderBottomSheet from '../components/OrderBottomSheet';
import OrderDetailsBottomSheet from '../components/OrderDetailsBottomSheet';
import PDFInvoiceBottomSheet from '../components/PDFInvoiceBottomSheet';
import { PDFInvoiceGenerator } from '../utils/pdfInvoiceGenerator';
import CommonHeader from '../components/CommonHeader';

const OrdersScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('All');
  const orderBottomSheetRef = useRef(null);
  const orderDetailsBottomSheetRef = useRef(null);
  const invoiceBottomSheetRef = useRef(null);
  const [editingOrder, setEditingOrder] = useState(null);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [invoiceOrder, setInvoiceOrder] = useState(null);

  const statuses = ['All', 'Pending', 'In Progress', 'Completed', 'Cancelled'];

  const filteredOrders = useMemo(() => {
    return state.orders.filter(order => {
      const matchesSearch = order.customer.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           order.id.includes(searchQuery);
      const matchesStatus = selectedStatus === 'All' || order.status === selectedStatus;
      return matchesSearch && matchesStatus;
    });
  }, [state.orders, searchQuery, selectedStatus]);

  const handleAddOrder = () => {
    setEditingOrder(null);
    orderBottomSheetRef.current?.expand();
  };

  const handleOrderPress = useCallback((order) => {
    setSelectedOrder(order);
    orderDetailsBottomSheetRef.current?.expand();
  }, []);

  const handleEditOrder = useCallback((order) => {
    setEditingOrder(order);
    orderBottomSheetRef.current?.expand();
  }, []);

  const handleDeleteOrder = useCallback((order) => {
    Alert.alert(
      'Delete Order',
      `Are you sure you want to delete order #${order.id}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => actions.deleteOrder(order.id),
        },
      ]
    );
  }, [actions]);

  const handleUpdateOrderStatus = useCallback((orderId, newStatus) => {
    actions.updateOrderStatus(orderId, newStatus);
  }, [actions]);

  const handleViewInvoice = useCallback((order) => {
    console.log('Opening invoice for order:', order.id);
    setInvoiceOrder(order);
    invoiceBottomSheetRef.current?.expand();
  }, []);

  const handleGeneratePDF = async (order) => {
    try {
      const pdf = await PDFInvoiceGenerator.generatePDF(order);
      Alert.alert(
        'PDF Generated',
        `Invoice PDF saved successfully!\nLocation: ${pdf.filePath}`,
        [
          { text: 'OK' },
          {
            text: 'Share',
            onPress: async () => {
              try {
                await PDFInvoiceGenerator.generateAndSharePDF(order);
              } catch (error) {
                Alert.alert('Share Error', 'Failed to share PDF invoice.');
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error('PDF generation error:', error);
      Alert.alert('PDF Error', 'Failed to generate PDF invoice. Please try again.');
    }
  };

  const handleSaveOrder = (orderData) => {
    if (editingOrder) {
      actions.updateOrder(orderData);
    } else {
      actions.addOrder(orderData);
    }
    setEditingOrder(null);
  };

  const getStatusColor = useCallback((status) => {
    switch (status) {
      case 'Completed':
        return {
          bg: theme.colors.tertiary, // Green - Success
          text: theme.colors.onTertiary,
        };
      case 'In Progress':
        return {
          bg: theme.colors.secondary, // Orange - In Progress
          text: theme.colors.onSecondary,
        };
      case 'Pending':
        return {
          bg: theme.colors.primary, // Blue - Waiting
          text: theme.colors.onPrimary,
        };
      case 'Cancelled':
        return {
          bg: theme.colors.error, // Red - Error/Cancelled
          text: theme.colors.onError,
        };
      default:
        return {
          bg: theme.colors.surfaceVariant,
          text: theme.colors.onSurfaceVariant,
        };
    }
  }, [theme]);

  const renderOrderCard = useCallback(({ item: order }) => {
    const statusColors = getStatusColor(order.status);

    return (
      <TouchableOpacity onPress={() => handleOrderPress(order)}>
        <Surface style={[styles.orderCard, { backgroundColor: theme.colors.surface }]} elevation={0}>
          {order.image && (
            <Image source={{ uri: order.image }} style={styles.orderImage} />
          )}
          <View style={styles.orderContent}>
          <View style={styles.orderHeader}>
            <View style={styles.orderInfo}>
              <Text variant="titleMedium" style={{ fontWeight: '600' }}>#{order.id}</Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
                {order.date} at {order.time}
              </Text>
            </View>
            <View style={styles.orderMeta}>
              <Text variant="titleLarge" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                ${order.total.toFixed(2)}
              </Text>
              <View style={[styles.statusBadge, { backgroundColor: statusColors.bg }]}>
                <Text variant="bodySmall" style={{ color: statusColors.text, fontWeight: '600' }}>
                  {order.status}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.customerSection}>
            <Text variant="bodyMedium" style={{ fontWeight: '500' }}>{order.customer}</Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              {order.phone}
            </Text>
          </View>

          <View style={styles.itemsSection}>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginBottom: 8 }}>
              {order.items.length} item{order.items.length !== 1 ? 's' : ''}
            </Text>
            {order.items.slice(0, 2).map((item, index) => (
              <Text key={index} variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                {item.name} x{item.quantity}
              </Text>
            ))}
            {order.items.length > 2 && (
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                +{order.items.length - 2} more items
              </Text>
            )}
          </View>

          <View style={styles.actionButtons}>
            <View style={styles.actionButtonsLeft}>
              <IconButton
                icon="pencil"
                size={20}
                iconColor={theme.colors.onSurfaceVariant}
                onPress={() => handleEditOrder(order)}
              />
              <IconButton
                icon="delete"
                size={20}
                iconColor={theme.colors.error}
                onPress={() => handleDeleteOrder(order)}
              />
            </View>

            <View style={styles.actionButtonsRight}>
              {order.status === 'Pending' && (
                <>
                  <Button
                    mode="outlined"
                    onPress={() => handleUpdateOrderStatus(order.id, 'Cancelled')}
                    compact
                    style={styles.actionButton}
                  >
                    Cancel
                  </Button>
                  <Button
                    mode="contained"
                    onPress={() => handleUpdateOrderStatus(order.id, 'In Progress')}
                    compact
                    style={styles.primaryActionButton}
                  >
                    Start
                  </Button>
                </>
              )}
              {order.status === 'In Progress' && (
                <Button
                  mode="contained"
                  onPress={() => handleUpdateOrderStatus(order.id, 'Completed')}
                  compact
                  style={styles.primaryActionButton}
                >
                  Complete
                </Button>
              )}
              {order.status === 'Completed' && (
                <Button
                  mode="contained"
                  onPress={() => handleViewInvoice(order)}
                  compact
                  style={styles.primaryActionButton}
                  icon="receipt"
                  buttonColor={theme.colors.tertiary}
                  textColor={theme.colors.onTertiary}
                >
                  Invoice
                </Button>
              )}
              {order.status === 'Cancelled' && (
                <Button
                  mode="outlined"
                  onPress={() => handleUpdateOrderStatus(order.id, 'Pending')}
                  compact
                  style={styles.actionButton}
                >
                  Reactivate
                </Button>
              )}
            </View>
          </View>
        </View>
      </Surface>
      </TouchableOpacity>
    );
  }, [theme, handleOrderPress, handleEditOrder, handleDeleteOrder, handleViewInvoice, handleUpdateOrderStatus, getStatusColor]);

  const orderStats = useMemo(() => ({
    total: filteredOrders.length,
    pending: filteredOrders.filter(o => o.status === 'Pending').length,
    inProgress: filteredOrders.filter(o => o.status === 'In Progress').length,
    completed: filteredOrders.filter(o => o.status === 'Completed').length,
    revenue: filteredOrders.reduce((sum, order) => sum + order.total, 0),
  }), [filteredOrders]);



  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Orders"
        subtitle="Track and manage orders"
        searchPlaceholder="Search orders..."
        searchValue={searchQuery}
        onSearchChange={setSearchQuery}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => console.log('Profile pressed')}
      />

      <FlatList
        data={filteredOrders}
        renderItem={renderOrderCard}
        keyExtractor={(item) => item.id.toString()}
        style={styles.content}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        initialNumToRender={8}
        windowSize={10}
        ListHeaderComponent={() => (
          <View>
            {/* Status Filter */}
            <ScrollView
              horizontal
              showsHorizontalScrollIndicator={false}
              style={[styles.statusContainer, { marginBottom: 16 }]}
            >
              {statuses.map((status) => (
                <Chip
                  key={status}
                  selected={selectedStatus === status}
                  onPress={() => setSelectedStatus(status)}
                  style={styles.statusChip}
                  mode={selectedStatus === status ? 'flat' : 'outlined'}
                >
                  {status}
                </Chip>
              ))}
            </ScrollView>

            {/* Stats Row */}
            <View style={styles.statsRow}>
              <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
                <Text variant="headlineSmall" style={{ color: theme.colors.primary }}>
                  {orderStats.total}
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Total Orders
                </Text>
              </Surface>

              <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
                <Text variant="headlineSmall" style={{ color: theme.colors.secondary }}>
                  ${orderStats.revenue.toFixed(0)}
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Revenue
                </Text>
              </Surface>
            </View>
          </View>
        )}
      />



      <OrderBottomSheet
        bottomSheetRef={orderBottomSheetRef}
        onSave={handleSaveOrder}
        order={editingOrder}
        mode={editingOrder ? 'edit' : 'add'}
      />

      <OrderDetailsBottomSheet
        bottomSheetRef={orderDetailsBottomSheetRef}
        order={selectedOrder}
        onUpdateStatus={handleUpdateOrderStatus}
        onEdit={handleEditOrder}
        onDelete={handleDeleteOrder}
      />

      <PDFInvoiceBottomSheet
        ref={invoiceBottomSheetRef}
        order={invoiceOrder}
        onClose={() => setInvoiceOrder(null)}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchbar: {
    flex: 1,
    borderRadius: 25,
    height: 48,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statusContainer: {
    marginBottom: 8,
  },
  statusChip: {
    marginRight: 6,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    padding: 16,
    marginHorizontal: 4,
    borderRadius: 12,
    alignItems: 'center',
  },
  orderCard: {
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    overflow: 'hidden',
  },
  orderImage: {
    width: '100%',
    height: 120,
    resizeMode: 'cover',
  },
  orderContent: {
    padding: 16,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  orderInfo: {
    flex: 1,
  },
  orderMeta: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 4,
    minWidth: 60,
    alignItems: 'center',
  },
  customerSection: {
    marginBottom: 12,
  },
  itemsSection: {
    marginBottom: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  actionButtonsLeft: {
    flexDirection: 'row',
    gap: 4,
  },
  actionButtonsRight: {
    flexDirection: 'row',
    gap: 6,
  },
  actionButton: {
    minWidth: 70,
  },
  primaryActionButton: {
    minWidth: 80,
  },
  pdfActionButton: {
    minWidth: 60,
  },
});

export default OrdersScreen;
