import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, Alert, Linking } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  Switch,
  List,
  Button,
  Surface,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import ActivityLogModal from '../components/ActivityLogModal';
import ImportDataModal from '../components/ImportDataModal';
import CommonHeader from '../components/CommonHeader';

const SettingsScreen = ({ navigation }) => {
  const { theme, isDarkMode, toggleTheme } = useTheme();
  const { state, actions } = useData();



  // Modal states
  const [activityLogModalVisible, setActivityLogModalVisible] = useState(false);
  const [importDataModalVisible, setImportDataModalVisible] = useState(false);

  const handleSettingChange = (setting, value) => {
    switch (setting) {
      case 'darkMode':
        toggleTheme();
        Alert.alert('Theme Changed', `Switched to ${value ? 'dark' : 'light'} mode!`);
        break;
    }
  };



  const handleExportData = () => {
    const dataToExport = actions.exportData();

    Alert.alert(
      'Export Data',
      `Export ${state.products.length} products, ${state.orders.length} orders, and ${state.customers.length} customers?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Export',
          onPress: () => {
            // In a real app, this would save to file or send via email
            console.log('Exported data:', dataToExport);
            Alert.alert('Success', 'Data exported successfully!');
          },
        },
      ]
    );
  };



  const handleContactSupport = () => {
    Alert.alert(
      'Contact Support',
      'How would you like to contact support?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Email',
          onPress: () => Linking.openURL('mailto:<EMAIL>'),
        },
        {
          text: 'Phone',
          onPress: () => Linking.openURL('tel:******-SUPPORT'),
        },
      ]
    );
  };

  const handleAbout = () => {
    Alert.alert(
      'About Sweet Delights Bakery Management',
      'Version: 1.0.0\nBuild: 2024.01.15\n\nA comprehensive bakery management system built with React Native and Material Design 3.\n\nDeveloped with ❤️ for bakery owners.',
      [{ text: 'OK' }]
    );
  };

  const handleImportData = (importData) => {
    try {
      // Use the new importData function from DataContext
      actions.importData(importData);
      Alert.alert('Success', 'Data imported successfully!');
    } catch (error) {
      console.error('Error importing data:', error);
      Alert.alert('Error', 'Failed to import data. Please check the format and try again.');
    }
  };



  const SettingsSection = ({ title, children }) => (
    <View style={styles.sectionContainer}>
      <Text variant="titleLarge" style={[styles.sectionTitle, { color: theme.colors.onBackground }]}>
        {title}
      </Text>
      <Surface style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]} elevation={0}>
        {children}
      </Surface>
    </View>
  );

  const SettingItem = ({ icon, title, subtitle, rightComponent, onPress, iconColor }) => (
    <Surface style={styles.settingItem} elevation={0}>
      <List.Item
        title={title}
        description={subtitle}
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: (iconColor || theme.colors.primary) + '15' }]}>
            <Icon name={icon} size={24} color={iconColor || theme.colors.primary} />
          </View>
        )}
        right={() => rightComponent || <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
        onPress={onPress}
        style={styles.listItem}
      />
    </Surface>
  );



  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Settings"
        subtitle="App preferences"
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => navigation.navigate('MyProfile')}
      />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <SettingsSection title="App Preferences">
          <SettingItem
            icon="theme-light-dark"
            title="Dark Mode"
            subtitle="Switch between light and dark themes"
            rightComponent={
              <Switch
                value={isDarkMode}
                onValueChange={(value) => handleSettingChange('darkMode', value)}
                color={theme.colors.primary}
              />
            }
          />
        </SettingsSection>



        <SettingsSection title="Data Management">
          <SettingItem
            icon="database-import"
            title="Import Data"
            subtitle="Import products, orders, and customers"
            onPress={() => setImportDataModalVisible(true)}
          />
          <SettingItem
            icon="database-export"
            title="Export Data"
            subtitle="Download your business data"
            onPress={handleExportData}
          />
          <SettingItem
            icon="history"
            title="Activity Log"
            subtitle="View recent system activities"
            onPress={() => setActivityLogModalVisible(true)}
          />
        </SettingsSection>

        <SettingsSection title="Support">
          <SettingItem
            icon="help-circle"
            title="Help & FAQ"
            subtitle="Get help and find answers"
            onPress={() => Alert.alert('Help & FAQ', 'Help documentation would be available here')}
          />
          <SettingItem
            icon="email"
            title="Contact Support"
            subtitle="Get in touch with our team"
            onPress={handleContactSupport}
          />
          <SettingItem
            icon="information"
            title="About"
            subtitle="App version and information"
            onPress={handleAbout}
          />
        </SettingsSection>

        <View style={styles.footer}>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center' }}>
            Business Management App v1.0.0
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 4 }}>
            Made with ❤️ for business owners
          </Text>
        </View>

        {/* Modals */}

      <ActivityLogModal
        visible={activityLogModalVisible}
        onDismiss={() => setActivityLogModalVisible(false)}
      />

      <ImportDataModal
        visible={importDataModalVisible}
        onDismiss={() => setImportDataModalVisible(false)}
        onImport={handleImportData}
      />
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    padding: 20,
  },

  statsContainer: {
    marginBottom: 24,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    padding: 16,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    alignItems: 'center',
    marginBottom: 12,
  },
  statIcon: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: 12,
  },
  sectionCard: {
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    overflow: 'hidden',
  },
  settingItem: {
    borderRadius: 0,
    marginBottom: 1,
  },
  listItem: {
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  footer: {
    marginTop: 32,
    marginBottom: 20,
  },
  reportsContainer: {
    marginBottom: 24,
  },
  reportsCard: {
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  reportsContent: {
    padding: 20,
  },
  reportsInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  reportsIcon: {
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  reportsText: {
    flex: 1,
  },
  reportsButton: {
    borderRadius: 8,
  },
});

export default SettingsScreen;
