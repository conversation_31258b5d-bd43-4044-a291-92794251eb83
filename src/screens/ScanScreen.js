/**
 * ScanScreen - QR/Barcode scanning functionality
 * Handles product scanning, order scanning, and general QR code scanning
 */

import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Alert, Dimensions } from 'react-native';
import { Text, Button, Card, useTheme } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';

// Try to import camera modules, but handle gracefully if not available
let Camera, BarCodeScanner;
let cameraAvailable = false;
try {
  const cameraModule = require('expo-camera');
  const barcodeScannerModule = require('expo-barcode-scanner');
  Camera = cameraModule.Camera;
  BarCodeScanner = barcodeScannerModule.BarCodeScanner;
  cameraAvailable = true;
} catch (error) {
  console.log('Camera modules not available in Expo Go:', error.message);
  Camera = null;
  BarCodeScanner = null;
  cameraAvailable = false;
}

const { width, height } = Dimensions.get('window');

const ScanScreen = ({ navigation }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { state, actions } = useData();
  const [isScanning, setIsScanning] = useState(false);
  const [scannedData, setScannedData] = useState(null);
  const [scanMode, setScanMode] = useState('product'); // 'product', 'order', 'general'
  const [hasPermission, setHasPermission] = useState(null);
  const [useCamera, setUseCamera] = useState(false);

  // Request camera permissions
  useEffect(() => {
    (async () => {
      if (!cameraAvailable || !Camera) {
        console.log('Camera not available, skipping permission request');
        setHasPermission(false);
        return;
      }

      try {
        const { status } = await Camera.requestCameraPermissionsAsync();
        setHasPermission(status === 'granted');
      } catch (error) {
        console.log('Camera permission error:', error);
        setHasPermission(false);
      }
    })();
  }, []);

  // Handle barcode scan
  const handleBarCodeScanned = ({ type, data }) => {
    setIsScanning(false);
    setUseCamera(false);

    // Process the scanned data based on scan mode
    let processedData;

    if (scanMode === 'product') {
      // Check if it's a product barcode
      const product = state.products.find(p => p.barcode === data);
      if (product) {
        processedData = {
          type: 'product',
          data: data,
          name: product.name,
          price: product.price,
          barcode: data
        };
      } else {
        processedData = {
          type: 'product',
          data: data,
          name: 'Unknown Product',
          price: 0,
          barcode: data
        };
      }
    } else if (scanMode === 'order') {
      processedData = {
        type: 'order',
        data: data,
        orderId: data,
        customer: 'Unknown Customer',
        status: 'Pending'
      };
    } else {
      processedData = {
        type: 'qr',
        data: data,
        url: data.startsWith('http') ? data : null
      };
    }

    setScannedData(processedData);
  };

  // Start camera scanning
  const startCameraScanning = async () => {
    if (!cameraAvailable || !Camera) {
      Alert.alert(
        'Camera Not Available',
        'Camera functionality is not available in Expo Go. Please use the demo scan instead, or build a development build to use the camera.',
        [
          { text: 'OK', style: 'default' },
          { text: 'Try Demo', onPress: simulateScanning }
        ]
      );
      return;
    }

    try {
      if (hasPermission === null) {
        const { status } = await Camera.requestCameraPermissionsAsync();
        setHasPermission(status === 'granted');
        if (status !== 'granted') {
          Alert.alert('Permission Required', 'Camera permission is required to scan codes.');
          return;
        }
      }

      if (hasPermission === false) {
        Alert.alert('Permission Denied', 'Camera permission is required to scan codes.');
        return;
      }

      setUseCamera(true);
      setIsScanning(true);
    } catch (error) {
      console.log('Camera start error:', error);
      Alert.alert('Camera Error', 'Unable to start camera. Please try the demo scan instead.');
      setHasPermission(false);
    }
  };

  // Simulate camera scanning (fallback for demo)
  const simulateScanning = () => {
    setIsScanning(true);

    // Simulate scanning delay
    setTimeout(() => {
      const mockScannedData = {
        product: {
          type: 'product',
          data: '1234567890123', // This matches French Baguette in sample data
          name: 'French Baguette',
          price: 4.50,
          barcode: '1234567890123'
        },
        order: {
          type: 'order',
          data: '001',
          orderId: '001',
          customer: 'John Doe',
          status: 'Pending'
        },
        general: {
          type: 'qr',
          data: 'https://sweetdelights.com/menu',
          url: 'https://sweetdelights.com/menu'
        }
      };

      setScannedData(mockScannedData[scanMode]);
      setIsScanning(false);
    }, 2000);
  };

  const handleScanResult = (data) => {
    switch (data.type) {
      case 'product':
        const isExistingProduct = data.name !== 'Unknown Product';
        Alert.alert(
          isExistingProduct ? 'Product Found' : 'New Product Detected',
          isExistingProduct
            ? `${data.name}\nPrice: $${data.price}\nBarcode: ${data.barcode}`
            : `Barcode: ${data.barcode}\nThis product is not in your inventory yet.`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: isExistingProduct ? 'View Product' : 'Add to Inventory',
              onPress: () => {
                if (isExistingProduct) {
                  navigation.navigate('Products');
                } else {
                  // Create new product with scanned barcode
                  const newProduct = {
                    id: Date.now().toString(),
                    name: `Product ${data.barcode.slice(-4)}`,
                    price: 0,
                    category: 'Uncategorized',
                    barcode: data.barcode,
                    stock: 0,
                    description: 'Product added via barcode scan'
                  };
                  actions.addProduct(newProduct);
                  Alert.alert('Success', 'Product added to inventory! You can edit the details in the Products section.');
                  navigation.navigate('Products');
                }
              }
            },
            {
              text: 'Create Order',
              onPress: () => {
                if (isExistingProduct) {
                  // Create order with existing product
                  navigation.navigate('Orders');
                } else {
                  Alert.alert('Info', 'Please add this product to inventory first before creating an order.');
                }
              }
            }
          ]
        );
        break;

      case 'order':
        // Check if order exists in the system
        const existingOrder = state.orders.find(order => order.id === data.orderId);
        Alert.alert(
          existingOrder ? 'Order Found' : 'Order Not Found',
          existingOrder
            ? `Order #${existingOrder.id}\nCustomer: ${existingOrder.customer}\nStatus: ${existingOrder.status}\nTotal: $${existingOrder.total.toFixed(2)}`
            : `Order ID: ${data.orderId}\nThis order is not found in your system.`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: existingOrder ? 'View Order' : 'Create New Order',
              onPress: () => {
                if (existingOrder) {
                  navigation.navigate('Orders');
                } else {
                  Alert.alert('Info', 'Redirecting to create a new order.');
                  navigation.navigate('Orders');
                }
              }
            }
          ]
        );
        break;

      case 'qr':
        const isUrl = data.url && data.url.startsWith('http');
        const isEmail = data.data.includes('@') && data.data.includes('.');
        const isPhone = /^\+?[\d\s\-\(\)]+$/.test(data.data);

        let title = 'QR Code Scanned';
        let message = `Data: ${data.data}`;
        let actionText = 'Copy';

        if (isUrl) {
          title = 'Website QR Code';
          message = `URL: ${data.url}`;
          actionText = 'Open Link';
        } else if (isEmail) {
          title = 'Email QR Code';
          message = `Email: ${data.data}`;
          actionText = 'Send Email';
        } else if (isPhone) {
          title = 'Phone QR Code';
          message = `Phone: ${data.data}`;
          actionText = 'Call';
        }

        Alert.alert(
          title,
          message,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: actionText,
              onPress: () => {
                if (isUrl) {
                  Alert.alert('Info', 'Would open: ' + data.url);
                } else if (isEmail) {
                  Alert.alert('Info', 'Would open email app for: ' + data.data);
                } else if (isPhone) {
                  Alert.alert('Info', 'Would open phone app for: ' + data.data);
                } else {
                  Alert.alert('Copied', 'QR code data copied to clipboard (simulated)');
                }
              }
            }
          ]
        );
        break;

      default:
        Alert.alert('Unknown Code', 'Scanned data could not be recognized.');
    }
  };

  const resetScan = () => {
    setScannedData(null);
    setIsScanning(false);
    setUseCamera(false);
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Scan Code"
        subtitle="QR & Barcode Scanner"
        showSearch={false}
      />

      {/* Info Banner for Expo Go */}
      {!cameraAvailable && (
        <View style={[styles.infoBanner, { backgroundColor: theme.colors.primaryContainer }]}>
          <Icon name="information" size={20} color={theme.colors.onPrimaryContainer} />
          <Text variant="bodySmall" style={[styles.infoBannerText, { color: theme.colors.onPrimaryContainer }]}>
            Camera not available in Expo Go. Use "Demo Scan" to test functionality.
          </Text>
        </View>
      )}

      {/* Scan Mode Selector */}
      <View style={styles.modeSelector}>
        <Text variant="titleMedium" style={[styles.modeTitle, { color: theme.colors.onSurface }]}>
          Scan Mode
        </Text>
        <View style={styles.modeButtons}>
          {[
            { key: 'product', label: 'Product', icon: 'package-variant' },
            { key: 'order', label: 'Order', icon: 'clipboard-list' },
            { key: 'general', label: 'QR Code', icon: 'qrcode' }
          ].map((mode) => (
            <Button
              key={mode.key}
              mode={scanMode === mode.key ? 'contained' : 'outlined'}
              onPress={() => setScanMode(mode.key)}
              icon={mode.icon}
              style={styles.modeButton}
              compact
            >
              {mode.label}
            </Button>
          ))}
        </View>
      </View>

      {/* Scanner Area */}
      <View style={styles.scannerContainer}>
        <Card style={styles.scannerCard}>
          <Card.Content style={styles.scannerContent}>
            {useCamera && isScanning ? (
              <View style={styles.cameraContainer}>
                {hasPermission && cameraAvailable && Camera ? (
                  <Camera
                    style={styles.camera}
                    type={Camera.Constants?.Type?.back || 'back'}
                    barCodeScannerSettings={{
                      barCodeTypes: [
                        BarCodeScanner.Constants?.BarCodeType?.qr || 'qr',
                        BarCodeScanner.Constants?.BarCodeType?.ean13 || 'ean13',
                        BarCodeScanner.Constants?.BarCodeType?.ean8 || 'ean8',
                        BarCodeScanner.Constants?.BarCodeType?.code128 || 'code128'
                      ],
                    }}
                    onBarCodeScanned={isScanning ? handleBarCodeScanned : undefined}
                    onCameraReady={() => console.log('Camera ready')}
                    onMountError={(error) => {
                      console.log('Camera mount error:', error);
                      Alert.alert('Camera Error', 'Unable to access camera. Please try the demo scan instead.');
                      setUseCamera(false);
                      setIsScanning(false);
                    }}
                  >
                    <View style={styles.scannerOverlay}>
                      <View style={styles.scannerFrame} />
                      <Text style={[styles.scannerInstructions, { color: 'white' }]}>
                        Point camera at {scanMode === 'general' ? 'QR code' : scanMode}
                      </Text>
                    </View>
                  </Camera>
                ) : (
                  <View style={styles.cameraError}>
                    <Icon name="camera-off" size={60} color={theme.colors.error} />
                    <Text variant="titleMedium" style={[styles.errorText, { color: theme.colors.error }]}>
                      Camera Not Available
                    </Text>
                    <Text variant="bodyMedium" style={[styles.errorSubtext, { color: theme.colors.onSurfaceVariant }]}>
                      {!cameraAvailable
                        ? 'Camera modules not available in Expo Go. Use demo scan or build a development build.'
                        : 'Please use the demo scan instead'
                      }
                    </Text>
                  </View>
                )}
              </View>
            ) : isScanning ? (
              <View style={styles.scanningState}>
                <Icon name="qrcode-scan" size={80} color={theme.colors.primary} />
                <Text variant="titleMedium" style={[styles.scanningText, { color: theme.colors.primary }]}>
                  Scanning...
                </Text>
                <View style={styles.scanningAnimation}>
                  <View style={[styles.scanLine, { backgroundColor: theme.colors.primary }]} />
                </View>
              </View>
            ) : scannedData ? (
              <View style={styles.resultState}>
                <Icon name="check-circle" size={60} color={theme.colors.primary} />
                <Text variant="titleMedium" style={[styles.resultText, { color: theme.colors.onSurface }]}>
                  Code Scanned Successfully!
                </Text>
                <Text variant="bodyMedium" style={[styles.resultData, { color: theme.colors.onSurfaceVariant }]}>
                  {scannedData.data}
                </Text>
                <View style={styles.resultActions}>
                  <Button
                    mode="contained"
                    onPress={() => handleScanResult(scannedData)}
                    style={styles.actionButton}
                  >
                    Process
                  </Button>
                  <Button
                    mode="outlined"
                    onPress={resetScan}
                    style={styles.actionButton}
                  >
                    Scan Again
                  </Button>
                </View>
              </View>
            ) : (
              <View style={styles.idleState}>
                <Icon name="qrcode-scan" size={80} color={theme.colors.onSurfaceVariant} />
                <Text variant="titleMedium" style={[styles.idleText, { color: theme.colors.onSurface }]}>
                  Ready to Scan
                </Text>
                <Text variant="bodyMedium" style={[styles.idleSubtext, { color: theme.colors.onSurfaceVariant }]}>
                  Point your camera at a {scanMode === 'general' ? 'QR code' : scanMode}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        {!isScanning && !scannedData && (
          <View style={styles.actionButtons}>
            <Button
              mode={cameraAvailable ? "contained" : "outlined"}
              onPress={startCameraScanning}
              icon="camera"
              style={[styles.scanButton, { flex: 1 }]}
              contentStyle={styles.scanButtonContent}
              disabled={!cameraAvailable}
            >
              {cameraAvailable ? "Camera Scan" : "Camera (N/A)"}
            </Button>
            <Button
              mode={cameraAvailable ? "outlined" : "contained"}
              onPress={simulateScanning}
              icon="play"
              style={[styles.scanButton, { flex: 1 }]}
              contentStyle={styles.scanButtonContent}
            >
              Demo Scan
            </Button>
          </View>
        )}

        {isScanning && (
          <Button
            mode="outlined"
            onPress={() => {
              setIsScanning(false);
              setUseCamera(false);
            }}
            icon="stop"
            style={styles.scanButton}
          >
            Stop Scanning
          </Button>
        )}
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <Text variant="titleSmall" style={[styles.quickActionsTitle, { color: theme.colors.onSurface }]}>
          Quick Actions
        </Text>
        <View style={styles.quickActionButtons}>
          <Button
            mode="outlined"
            icon="package-plus"
            onPress={() => navigation.navigate('Products')}
            style={styles.quickActionButton}
            compact
          >
            Add Product
          </Button>
          <Button
            mode="outlined"
            icon="clipboard-list-outline"
            onPress={() => navigation.navigate('Orders')}
            style={styles.quickActionButton}
            compact
          >
            New Order
          </Button>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  infoBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    marginHorizontal: 16,
    marginTop: 8,
    borderRadius: 8,
    gap: 8,
  },
  infoBannerText: {
    flex: 1,
    fontSize: 12,
  },
  modeSelector: {
    padding: 16,
  },
  modeTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  modeButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  modeButton: {
    flex: 1,
  },
  scannerContainer: {
    flex: 1,
    padding: 16,
  },
  scannerCard: {
    flex: 1,
    borderRadius: 16,
  },
  scannerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningState: {
    alignItems: 'center',
  },
  scanningText: {
    marginTop: 16,
    fontWeight: '600',
  },
  scanningAnimation: {
    marginTop: 20,
    width: 200,
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  scanLine: {
    height: '100%',
    width: '30%',
    borderRadius: 2,
  },
  resultState: {
    alignItems: 'center',
  },
  resultText: {
    marginTop: 16,
    fontWeight: '600',
  },
  resultData: {
    marginTop: 8,
    textAlign: 'center',
  },
  resultActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  actionButton: {
    minWidth: 100,
  },
  idleState: {
    alignItems: 'center',
  },
  idleText: {
    marginTop: 16,
    fontWeight: '600',
  },
  idleSubtext: {
    marginTop: 8,
    textAlign: 'center',
  },
  actionContainer: {
    padding: 16,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  scanButton: {
    borderRadius: 12,
  },
  scanButtonContent: {
    height: 48,
  },
  cameraContainer: {
    flex: 1,
    width: '100%',
    borderRadius: 12,
    overflow: 'hidden',
  },
  camera: {
    flex: 1,
    minHeight: 300,
  },
  scannerOverlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  scannerFrame: {
    width: 200,
    height: 200,
    borderWidth: 2,
    borderColor: 'white',
    borderRadius: 12,
    backgroundColor: 'transparent',
  },
  scannerInstructions: {
    marginTop: 20,
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
    textShadowColor: 'rgba(0,0,0,0.8)',
    textShadowOffset: { width: 1, height: 1 },
    textShadowRadius: 2,
  },
  cameraError: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 300,
  },
  errorText: {
    marginTop: 16,
    fontWeight: '600',
  },
  errorSubtext: {
    marginTop: 8,
    textAlign: 'center',
  },
  quickActions: {
    padding: 16,
    paddingTop: 0,
  },
  quickActionsTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  quickActionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  quickActionButton: {
    flex: 1,
  },
});

export default ScanScreen;
