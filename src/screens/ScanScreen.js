/**
 * ScanScreen - QR/Barcode scanning functionality
 * Handles product scanning, order scanning, and general QR code scanning
 */

import React, { useState, useEffect, useRef } from 'react';
import { View, StyleSheet, Alert, Dimensions } from 'react-native';
import { Text, Button, Card, useTheme } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { Camera } from 'expo-camera';
import { BarCodeScanner } from 'expo-barcode-scanner';
import { useData } from '../context/DataContext';
import CommonHeader from '../components/CommonHeader';

const { width, height } = Dimensions.get('window');

const ScanScreen = ({ navigation }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { state, actions } = useData();
  const [isScanning, setIsScanning] = useState(false);
  const [scannedData, setScannedData] = useState(null);
  const [scanMode, setScanMode] = useState('product'); // 'product', 'order', 'general'
  const [hasPermission, setHasPermission] = useState(null);
  const [useCamera, setUseCamera] = useState(false);

  // Request camera permissions
  useEffect(() => {
    (async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
    })();
  }, []);

  // Handle barcode scan
  const handleBarCodeScanned = ({ type, data }) => {
    setIsScanning(false);
    setUseCamera(false);

    // Process the scanned data based on scan mode
    let processedData;

    if (scanMode === 'product') {
      // Check if it's a product barcode
      const product = state.products.find(p => p.barcode === data);
      if (product) {
        processedData = {
          type: 'product',
          data: data,
          name: product.name,
          price: product.price,
          barcode: data
        };
      } else {
        processedData = {
          type: 'product',
          data: data,
          name: 'Unknown Product',
          price: 0,
          barcode: data
        };
      }
    } else if (scanMode === 'order') {
      processedData = {
        type: 'order',
        data: data,
        orderId: data,
        customer: 'Unknown Customer',
        status: 'Pending'
      };
    } else {
      processedData = {
        type: 'qr',
        data: data,
        url: data.startsWith('http') ? data : null
      };
    }

    setScannedData(processedData);
  };

  // Start camera scanning
  const startCameraScanning = async () => {
    if (hasPermission === null) {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setHasPermission(status === 'granted');
      if (status !== 'granted') {
        Alert.alert('Permission Required', 'Camera permission is required to scan codes.');
        return;
      }
    }

    if (hasPermission === false) {
      Alert.alert('Permission Denied', 'Camera permission is required to scan codes.');
      return;
    }

    setUseCamera(true);
    setIsScanning(true);
  };

  // Simulate camera scanning (fallback for demo)
  const simulateScanning = () => {
    setIsScanning(true);

    // Simulate scanning delay
    setTimeout(() => {
      const mockScannedData = {
        product: {
          type: 'product',
          data: 'PROD_001',
          name: 'Chocolate Croissant',
          price: 4.50,
          barcode: '1234567890123'
        },
        order: {
          type: 'order',
          data: 'ORD_001',
          orderId: '001',
          customer: 'John Doe',
          status: 'Pending'
        },
        general: {
          type: 'qr',
          data: 'https://sweetdelights.com/menu',
          url: 'https://sweetdelights.com/menu'
        }
      };

      setScannedData(mockScannedData[scanMode]);
      setIsScanning(false);
    }, 2000);
  };

  const handleScanResult = (data) => {
    switch (data.type) {
      case 'product':
        Alert.alert(
          'Product Found',
          `${data.name}\nPrice: $${data.price}\nBarcode: ${data.barcode}`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Add to Inventory',
              onPress: () => {
                // Add product logic here
                Alert.alert('Success', 'Product added to inventory!');
                navigation.navigate('Products');
              }
            },
            {
              text: 'Create Order',
              onPress: () => {
                // Create order with this product
                Alert.alert('Success', 'Order created with this product!');
                navigation.navigate('Orders');
              }
            }
          ]
        );
        break;

      case 'order':
        Alert.alert(
          'Order Found',
          `Order ID: ${data.orderId}\nCustomer: ${data.customer}\nStatus: ${data.status}`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'View Order',
              onPress: () => {
                Alert.alert('Success', 'Opening order details!');
                navigation.navigate('Orders');
              }
            }
          ]
        );
        break;

      case 'qr':
        Alert.alert(
          'QR Code Scanned',
          `URL: ${data.url}`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Open Link',
              onPress: () => {
                Alert.alert('Info', 'Would open: ' + data.url);
              }
            }
          ]
        );
        break;

      default:
        Alert.alert('Unknown Code', 'Scanned data could not be recognized.');
    }
  };

  const resetScan = () => {
    setScannedData(null);
    setIsScanning(false);
    setUseCamera(false);
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Scan Code"
        subtitle="QR & Barcode Scanner"
        showSearch={false}
      />

      {/* Scan Mode Selector */}
      <View style={styles.modeSelector}>
        <Text variant="titleMedium" style={[styles.modeTitle, { color: theme.colors.onSurface }]}>
          Scan Mode
        </Text>
        <View style={styles.modeButtons}>
          {[
            { key: 'product', label: 'Product', icon: 'package-variant' },
            { key: 'order', label: 'Order', icon: 'clipboard-list' },
            { key: 'general', label: 'QR Code', icon: 'qrcode' }
          ].map((mode) => (
            <Button
              key={mode.key}
              mode={scanMode === mode.key ? 'contained' : 'outlined'}
              onPress={() => setScanMode(mode.key)}
              icon={mode.icon}
              style={styles.modeButton}
              compact
            >
              {mode.label}
            </Button>
          ))}
        </View>
      </View>

      {/* Scanner Area */}
      <View style={styles.scannerContainer}>
        <Card style={styles.scannerCard}>
          <Card.Content style={styles.scannerContent}>
            {isScanning ? (
              <View style={styles.scanningState}>
                <Icon name="qrcode-scan" size={80} color={theme.colors.primary} />
                <Text variant="titleMedium" style={[styles.scanningText, { color: theme.colors.primary }]}>
                  Scanning...
                </Text>
                <View style={styles.scanningAnimation}>
                  <View style={[styles.scanLine, { backgroundColor: theme.colors.primary }]} />
                </View>
              </View>
            ) : scannedData ? (
              <View style={styles.resultState}>
                <Icon name="check-circle" size={60} color={theme.colors.primary} />
                <Text variant="titleMedium" style={[styles.resultText, { color: theme.colors.onSurface }]}>
                  Code Scanned Successfully!
                </Text>
                <Text variant="bodyMedium" style={[styles.resultData, { color: theme.colors.onSurfaceVariant }]}>
                  {scannedData.data}
                </Text>
                <View style={styles.resultActions}>
                  <Button
                    mode="contained"
                    onPress={() => handleScanResult(scannedData)}
                    style={styles.actionButton}
                  >
                    Process
                  </Button>
                  <Button
                    mode="outlined"
                    onPress={resetScan}
                    style={styles.actionButton}
                  >
                    Scan Again
                  </Button>
                </View>
              </View>
            ) : (
              <View style={styles.idleState}>
                <Icon name="qrcode-scan" size={80} color={theme.colors.onSurfaceVariant} />
                <Text variant="titleMedium" style={[styles.idleText, { color: theme.colors.onSurface }]}>
                  Ready to Scan
                </Text>
                <Text variant="bodyMedium" style={[styles.idleSubtext, { color: theme.colors.onSurfaceVariant }]}>
                  Point your camera at a {scanMode === 'general' ? 'QR code' : scanMode}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>
      </View>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        {!isScanning && !scannedData && (
          <Button
            mode="contained"
            onPress={simulateScanning}
            icon="camera"
            style={styles.scanButton}
            contentStyle={styles.scanButtonContent}
          >
            Start Scanning
          </Button>
        )}

        {isScanning && (
          <Button
            mode="outlined"
            onPress={() => setIsScanning(false)}
            icon="stop"
            style={styles.scanButton}
          >
            Stop Scanning
          </Button>
        )}
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <Text variant="titleSmall" style={[styles.quickActionsTitle, { color: theme.colors.onSurface }]}>
          Quick Actions
        </Text>
        <View style={styles.quickActionButtons}>
          <Button
            mode="outlined"
            icon="package-variant-plus"
            onPress={() => navigation.navigate('Products')}
            style={styles.quickActionButton}
            compact
          >
            Add Product
          </Button>
          <Button
            mode="outlined"
            icon="clipboard-list-outline"
            onPress={() => navigation.navigate('Orders')}
            style={styles.quickActionButton}
            compact
          >
            New Order
          </Button>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  modeSelector: {
    padding: 16,
  },
  modeTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  modeButtons: {
    flexDirection: 'row',
    gap: 8,
  },
  modeButton: {
    flex: 1,
  },
  scannerContainer: {
    flex: 1,
    padding: 16,
  },
  scannerCard: {
    flex: 1,
    borderRadius: 16,
  },
  scannerContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scanningState: {
    alignItems: 'center',
  },
  scanningText: {
    marginTop: 16,
    fontWeight: '600',
  },
  scanningAnimation: {
    marginTop: 20,
    width: 200,
    height: 4,
    backgroundColor: 'rgba(0,0,0,0.1)',
    borderRadius: 2,
    overflow: 'hidden',
  },
  scanLine: {
    height: '100%',
    width: '30%',
    borderRadius: 2,
  },
  resultState: {
    alignItems: 'center',
  },
  resultText: {
    marginTop: 16,
    fontWeight: '600',
  },
  resultData: {
    marginTop: 8,
    textAlign: 'center',
  },
  resultActions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
  },
  actionButton: {
    minWidth: 100,
  },
  idleState: {
    alignItems: 'center',
  },
  idleText: {
    marginTop: 16,
    fontWeight: '600',
  },
  idleSubtext: {
    marginTop: 8,
    textAlign: 'center',
  },
  actionContainer: {
    padding: 16,
  },
  scanButton: {
    borderRadius: 12,
  },
  scanButtonContent: {
    height: 48,
  },
  quickActions: {
    padding: 16,
    paddingTop: 0,
  },
  quickActionsTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  quickActionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  quickActionButton: {
    flex: 1,
  },
});

export default ScanScreen;
