import React, { useState, useRef, useCallback, useMemo } from 'react';
import { FlatList, View, StyleSheet, Alert, RefreshControl, TouchableOpacity } from 'react-native';
import {
  Card,
  Text,
  Button,
  Surface,
  IconButton,
  Menu,
  Avatar,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import UnifiedFilterChips from '../components/UnifiedFilterChips';
import UnifiedEmptyState from '../components/UnifiedEmptyState';
import navigationService from '../services/NavigationService';
import { SPACING, BORDER_RADIUS, getBorderColor } from '../theme/designTokens';

const CustomersScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [menuVisible, setMenuVisible] = useState({});
  const [refreshing, setRefreshing] = useState(false);

  const filters = ['All', 'Active', 'New', 'VIP'];

  // Pull to refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Simulate data refresh - in real app, this would reload from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Customers data refreshed');
    } catch (error) {
      console.log('Failed to refresh customers data:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  const filteredCustomers = useMemo(() => {
    return state.customers.filter(customer => {
      const matchesSearch = customer.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           customer.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           customer.phone.includes(searchQuery);
      const matchesFilter = selectedFilter === 'All' ||
                           (selectedFilter === 'Active' && customer.status === 'active') ||
                           (selectedFilter === 'New' && customer.isNew) ||
                           (selectedFilter === 'VIP' && customer.isVIP);
      return matchesSearch && matchesFilter;
    });
  }, [state.customers, searchQuery, selectedFilter]);

  const handleAddCustomer = () => {
    console.log('Navigating to Add Customer page...');
    try {
      navigationService.navigate('AddCustomer');
    } catch (error) {
      console.error('Failed to navigate to AddCustomer:', error);
    }
  };

  const handleEditCustomer = (customer) => {
    console.log('Navigating to Edit Customer page...', customer.id);
    try {
      navigationService.navigate('AddCustomer', { customer });
    } catch (error) {
      console.error('Failed to navigate to edit customer:', error);
    }
  };

  const handleDeleteCustomer = (customer) => {
    Alert.alert(
      'Delete Customer',
      `Are you sure you want to delete "${customer.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => actions.deleteCustomer(customer.id),
        },
      ]
    );
  };

  const handleViewOrders = (customer) => {
    console.log('Viewing orders for customer:', customer.name);
    try {
      navigationService.navigate('Orders', { customerId: customer.id });
    } catch (error) {
      console.error('Failed to navigate to customer orders:', error);
    }
  };

  const toggleMenu = (customerId) => {
    setMenuVisible(prev => ({
      ...prev,
      [customerId]: !prev[customerId]
    }));
  };

  const closeMenu = (customerId) => {
    setMenuVisible(prev => ({
      ...prev,
      [customerId]: false
    }));
  };

  const getCustomerInitials = (name) => {
    return name.split(' ').map(word => word[0]).join('').substring(0, 2).toUpperCase();
  };

  const getCustomerStats = (customer) => {
    const customerOrders = state.orders.filter(order =>
      order.customerName === customer.name || order.customer === customer.name
    );
    const totalSpent = customerOrders.reduce((sum, order) => sum + order.total, 0);
    return {
      totalOrders: customerOrders.length,
      totalSpent,
      lastOrder: customerOrders.length > 0 ? customerOrders[0].date : null
    };
  };

  const renderCustomerCard = useCallback(({ item: customer }) => {
    const stats = getCustomerStats(customer);

    return (
      <Surface style={[
        styles.customerCard,
        {
          backgroundColor: theme.colors.surface,
          borderColor: getBorderColor(theme),
        }
      ]} elevation={0}>
        <View style={styles.customerContent}>
          <View style={styles.customerHeader}>
            <View style={styles.customerAvatar}>
              {customer.avatar ? (
                <Avatar.Image size={48} source={{ uri: customer.avatar }} />
              ) : (
                <Avatar.Text
                  size={48}
                  label={getCustomerInitials(customer.name)}
                  style={{ backgroundColor: theme.colors.primary }}
                />
              )}
            </View>

            <View style={styles.customerInfo}>
              <View style={styles.customerNameRow}>
                <Text variant="titleMedium" style={{ fontWeight: '600', flex: 1 }}>
                  {customer.name}
                </Text>
                {customer.isVIP && (
                  <Chip
                    icon="crown"
                    style={{ backgroundColor: theme.colors.tertiary + '20' }}
                    textStyle={{ color: theme.colors.tertiary, fontSize: 10 }}
                    compact
                  >
                    VIP
                  </Chip>
                )}
              </View>

              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
                {customer.email}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                {customer.phone}
              </Text>
            </View>

            <Menu
              visible={menuVisible[customer.id] || false}
              onDismiss={() => closeMenu(customer.id)}
              anchor={
                <IconButton
                  icon="dots-vertical"
                  size={20}
                  iconColor={theme.colors.onSurfaceVariant}
                  onPress={() => toggleMenu(customer.id)}
                />
              }
            >
              <Menu.Item
                onPress={() => {
                  closeMenu(customer.id);
                  handleEditCustomer(customer);
                }}
                title="Edit"
                leadingIcon="pencil"
              />
              <Menu.Item
                onPress={() => {
                  closeMenu(customer.id);
                  handleViewOrders(customer);
                }}
                title="View Orders"
                leadingIcon="clipboard-list"
              />
              <Menu.Item
                onPress={() => {
                  closeMenu(customer.id);
                  handleDeleteCustomer(customer);
                }}
                title="Delete"
                leadingIcon="delete"
              />
            </Menu>
          </View>

          <View style={styles.customerStats}>
            <View style={styles.statItem}>
              <Text variant="titleSmall" style={{ color: theme.colors.primary, fontWeight: '600' }}>
                {stats.totalOrders}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Orders
              </Text>
            </View>

            <View style={styles.statItem}>
              <Text variant="titleSmall" style={{ color: theme.colors.secondary, fontWeight: '600' }}>
                ${stats.totalSpent.toFixed(0)}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Total Spent
              </Text>
            </View>

            <View style={styles.statItem}>
              <Text variant="titleSmall" style={{ color: theme.colors.tertiary, fontWeight: '600' }}>
                {stats.lastOrder || 'Never'}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Last Order
              </Text>
            </View>
          </View>

          <View style={styles.actionButtons}>
            <Button
              mode="outlined"
              onPress={() => handleViewOrders(customer)}
              icon="clipboard-list"
              style={styles.actionButton}
              compact
            >
              Orders
            </Button>
            <Button
              mode="contained"
              onPress={() => handleEditCustomer(customer)}
              icon="pencil"
              style={styles.actionButton}
              compact
            >
              Edit
            </Button>
          </View>
        </View>
      </Surface>
    );
  }, [theme, handleEditCustomer, handleDeleteCustomer, handleViewOrders, menuVisible, toggleMenu, closeMenu, state.orders]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Customers"
        subtitle="Manage your customer base"
        searchPlaceholder="Search customers..."
        searchType="customers"
        searchData={state.customers}
        searchFields={["name", "email", "phone"]}
        onSearchChange={setSearchQuery}
        onSearchResult={(customer) => {
          console.log('Customer selected from search:', customer);
          handleEditCustomer(customer);
        }}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          console.log('Profile pressed - navigating to MyProfile');
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <FlatList
        data={filteredCustomers}
        renderItem={renderCustomerCard}
        keyExtractor={(item) => item.id.toString()}
        style={styles.content}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        initialNumToRender={8}
        windowSize={10}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListHeaderComponent={() => (
          <View>
            {/* Filter Chips */}
            <UnifiedFilterChips
              filters={filters}
              selectedFilter={selectedFilter}
              onFilterChange={setSelectedFilter}
              showCounts={true}
              data={state.customers}
              countField="status"
              style={{ marginBottom: 16 }}
            />

            {/* Stats Row */}
            <View style={styles.statsRow}>
              <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
                <Text variant="headlineSmall" style={{ color: theme.colors.primary }}>
                  {filteredCustomers.length}
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Customers
                </Text>
              </Surface>

              <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
                <Text variant="headlineSmall" style={{ color: theme.colors.secondary }}>
                  {filteredCustomers.filter(c => c.isVIP).length}
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  VIP Members
                </Text>
              </Surface>
            </View>
          </View>
        )}
        ListEmptyComponent={() => (
          <UnifiedEmptyState
            type="customers"
            searchQuery={searchQuery}
            onActionPress={handleAddCustomer}
          />
        )}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: SPACING.lg,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.lg,
  },
  statCard: {
    flex: 1,
    padding: SPACING.lg,
    marginHorizontal: SPACING.xs,
    borderRadius: BORDER_RADIUS.xl,
    alignItems: 'center',
  },
  customerCard: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.xl,
    borderWidth: 1,
    overflow: 'hidden',
  },
  customerContent: {
    padding: SPACING.lg,
  },
  customerHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: SPACING.md,
  },
  customerAvatar: {
    marginRight: SPACING.md,
  },
  customerInfo: {
    flex: 1,
  },
  customerNameRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  customerStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: SPACING.md,
    paddingVertical: SPACING.sm,
    backgroundColor: 'rgba(0,0,0,0.02)',
    borderRadius: BORDER_RADIUS.md,
  },
  statItem: {
    alignItems: 'center',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: SPACING.sm,
  },
  actionButton: {
    flex: 1,
  },
});

export default CustomersScreen;
