import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { ScrollView, View, StyleSheet, RefreshControl } from 'react-native';
import {
  Text,
  Surface,
  Button,
  Card,
  useTheme,
  Chip,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useFinancial } from '../context/FinancialContext';
import { FINANCIAL_CONFIG } from '../config/constants';
import CommonHeader from '../components/CommonHeader';
import navigationService from '../services/NavigationService';
import ExpenseBottomSheet from '../components/ExpenseBottomSheet';
import CashReconciliationBottomSheet from '../components/CashReconciliationBottomSheet';
import ProfitLossBottomSheet from '../components/ProfitLossBottomSheet';
import PaymentAnalyticsBottomSheet from '../components/PaymentAnalyticsBottomSheet';
import TaxSummaryBottomSheet from '../components/TaxSummaryBottomSheet';
import UnifiedInfoCard from '../components/UnifiedInfoCard';

// Utility function for period dates - moved outside component for better performance
const getPeriodDates = (period) => {
  const now = new Date();
  let startDate, endDate;

  switch (period) {
    case 'week':
      startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7);
      endDate = now;
      break;
    case 'month':
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = now;
      break;
    case 'quarter':
      const quarter = Math.floor(now.getMonth() / 3);
      startDate = new Date(now.getFullYear(), quarter * 3, 1);
      endDate = now;
      break;
    case 'year':
      startDate = new Date(now.getFullYear(), 0, 1);
      endDate = now;
      break;
    default:
      startDate = new Date(now.getFullYear(), now.getMonth(), 1);
      endDate = now;
  }

  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0],
  };
};

// Currency formatter - moved outside for performance
const formatCurrency = (amount) => {
  return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}${amount.toFixed(FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES)}`;
};

const FinancialScreen = ({ navigation, navigateToTab }) => {
  const theme = useTheme();
  const {
    expenses,
    reconciliations,
    profitLossData,
    paymentAnalytics,
    taxSummary,
    derivedData,
    loading,
    loadExpenses,
    loadReconciliations,
    generateProfitLossStatement,
    getPaymentMethodAnalytics,
    getTaxSummary,
  } = useFinancial();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // Bottom sheet refs
  const expenseBottomSheetRef = useRef(null);
  const reconciliationBottomSheetRef = useRef(null);
  const profitLossBottomSheetRef = useRef(null);
  const paymentAnalyticsBottomSheetRef = useRef(null);
  const taxSummaryBottomSheetRef = useRef(null);

  // Memoize period dates to prevent unnecessary recalculations
  const periodDates = useMemo(() => getPeriodDates(selectedPeriod), [selectedPeriod]);

  // Optimized data loading with better performance
  const loadInitialData = useCallback(async () => {
    try {
      const { startDate, endDate } = periodDates;

      // Load critical data first for better perceived performance
      const criticalData = await Promise.allSettled([
        loadExpenses({ startDate, endDate }),
        generateProfitLossStatement(startDate, endDate),
      ]);

      // Load secondary data in background
      Promise.allSettled([
        loadReconciliations({ startDate, endDate }),
        getPaymentMethodAnalytics(startDate, endDate),
        getTaxSummary(startDate, endDate),
      ]).catch(error => {
        console.warn('Secondary data loading failed:', error);
      });

      // Log any critical data failures
      criticalData.forEach((result, index) => {
        if (result.status === 'rejected') {
          console.error(`Critical data loading failed at index ${index}:`, result.reason);
        }
      });
    } catch (error) {
      console.error('Failed to load financial data:', error);
    }
  }, [periodDates, loadExpenses, generateProfitLossStatement, loadReconciliations, getPaymentMethodAnalytics, getTaxSummary]);

  // Load data on mount and period change with optimized debouncing
  useEffect(() => {
    let mounted = true;
    let timeoutId;

    const loadData = async () => {
      if (mounted && !loading) {
        // Shorter debounce for better responsiveness
        timeoutId = setTimeout(async () => {
          if (mounted) {
            await loadInitialData();
          }
        }, 50);
      }
    };

    loadData();

    return () => {
      mounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [selectedPeriod, loadInitialData]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      await loadInitialData();
    } finally {
      setRefreshing(false);
    }
  }, [loadInitialData]);

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      {['week', 'month', 'quarter', 'year'].map(period => (
        <Chip
          key={period}
          selected={selectedPeriod === period}
          onPress={() => setSelectedPeriod(period)}
          style={[
            styles.periodChip,
            selectedPeriod === period && { backgroundColor: theme.colors.primary }
          ]}
          textStyle={{
            color: selectedPeriod === period ? theme.colors.onPrimary : theme.colors.onSurface
          }}
        >
          {period.charAt(0).toUpperCase() + period.slice(1)}
        </Chip>
      ))}
    </View>
  );

  // Memoize expensive render functions to prevent unnecessary re-renders
  const renderFinancialOverview = useMemo(() => (
    <Card style={[styles.overviewCard, { backgroundColor: theme.colors.surface }]}>
      <Card.Content>
        <Text variant="titleLarge" style={{ color: theme.colors.onSurface, marginBottom: 16 }}>
          Financial Overview
        </Text>

        <View style={styles.overviewGrid}>
          <View style={styles.statCardWrapper}>
            <UnifiedInfoCard
              type="stat"
              icon="trending-up-outline"
              iconColor={theme.colors.primary}
              value={formatCurrency(profitLossData?.revenue?.totalSales || 0)}
              title="Revenue"
              elevation={1}
            />
          </View>
          <View style={styles.statCardWrapper}>
            <UnifiedInfoCard
              type="stat"
              icon="trending-down-outline"
              iconColor="#F44336"
              value={formatCurrency(derivedData.totalExpenses)}
              title="Expenses"
              elevation={1}
            />
          </View>
          <View style={styles.statCardWrapper}>
            <UnifiedInfoCard
              type="stat"
              icon="chart-line-outline"
              iconColor={(profitLossData?.profit?.net || 0) >= 0 ? '#4CAF50' : '#F44336'}
              value={formatCurrency(profitLossData?.profit?.net || 0)}
              title="Net Profit"
              elevation={1}
            />
          </View>
          <View style={styles.statCardWrapper}>
            <UnifiedInfoCard
              type="stat"
              icon="percent-outline"
              iconColor="#9C27B0"
              value={`${(profitLossData?.profit?.margin || 0).toFixed(1)}%`}
              title="Profit Margin"
              elevation={1}
            />
          </View>
        </View>
      </Card.Content>
    </Card>
  ), [theme.colors, profitLossData, derivedData]);

  const renderQuickActions = useMemo(() => (
    <Card style={[styles.actionsCard, { backgroundColor: theme.colors.surface }]}>
      <Card.Content>
        <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginBottom: 16 }}>
          Quick Actions
        </Text>

        <View style={styles.actionsGrid}>
          <Button
            mode="contained-tonal"
            onPress={() => expenseBottomSheetRef.current?.expand()}
            icon="plus"
            style={styles.actionButton}
          >
            Add Expense
          </Button>

          <Button
            mode="contained-tonal"
            onPress={() => reconciliationBottomSheetRef.current?.expand()}
            icon="cash-register"
            style={styles.actionButton}
          >
            Cash Reconciliation
          </Button>

          <Button
            mode="contained-tonal"
            onPress={() => profitLossBottomSheetRef.current?.expand()}
            icon="chart-line"
            style={styles.actionButton}
          >
            P&L Statement
          </Button>

          <Button
            mode="contained-tonal"
            onPress={() => paymentAnalyticsBottomSheetRef.current?.expand()}
            icon="credit-card"
            style={styles.actionButton}
          >
            Payment Analytics
          </Button>

          <Button
            mode="contained-tonal"
            onPress={() => taxSummaryBottomSheetRef.current?.expand()}
            icon="calculator"
            style={styles.actionButton}
          >
            Tax Summary
          </Button>

          <Button
            mode="contained-tonal"
            onPress={() => {
              console.log('Navigating to Advanced Reports...');
              try {
                navigationService.navigate('Reports');
              } catch (error) {
                console.error('Failed to navigate to Reports:', error);
              }
            }}
            icon="file-chart"
            style={styles.actionButton}
          >
            Advanced Reports
          </Button>
        </View>
      </Card.Content>
    </Card>
  ), [theme.colors]);

  const renderRecentExpenses = useMemo(() => (
    <Card style={[styles.recentCard, { backgroundColor: theme.colors.surface }]}>
      <Card.Content>
        <View style={styles.cardHeader}>
          <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
            Recent Expenses
          </Text>
          <Button
            mode="text"
            onPress={() => expenseBottomSheetRef.current?.expand()}
            compact
          >
            View All
          </Button>
        </View>

        {expenses.slice(0, 3).map(expense => (
          <View key={expense.id} style={styles.expenseItem}>
            <View style={styles.expenseInfo}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                {expense.description}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                {expense.category} • {expense.date}
              </Text>
            </View>
            <Text variant="titleSmall" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {formatCurrency(expense.amount)}
            </Text>
          </View>
        ))}

        {expenses.length === 0 && (
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 16 }}>
            No expenses recorded yet
          </Text>
        )}
      </Card.Content>
    </Card>
  ), [theme.colors, expenses]);

  const renderCashReconciliationStatus = useMemo(() => {
    const latestReconciliation = reconciliations[0];

    return (
      <Card style={[styles.reconciliationCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <View style={styles.cardHeader}>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
              Cash Reconciliation
            </Text>
            <Chip
              icon={latestReconciliation?.status === 'balanced' ? 'check' : 'alert'}
              style={{
                backgroundColor: latestReconciliation?.status === 'balanced' ? '#4CAF50' : '#FF9800'
              }}
              textStyle={{ color: '#FFFFFF' }}
            >
              {latestReconciliation?.status || 'Pending'}
            </Chip>
          </View>

          {latestReconciliation ? (
            <View style={styles.reconciliationInfo}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                Last reconciliation: {latestReconciliation.date}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Difference: {formatCurrency(Math.abs(latestReconciliation.difference))}
              </Text>
            </View>
          ) : (
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              No reconciliation performed today
            </Text>
          )}
        </Card.Content>
      </Card>
    );
  }, [theme.colors, reconciliations]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <CommonHeader
        title="Financial Analytics"
        subtitle="Track your bakery's financial performance"
        showSearch={false}
      />

      <ScrollView
        style={styles.content}
        contentContainerStyle={{ paddingTop: 16 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderPeriodSelector()}
        {renderFinancialOverview}
        {renderQuickActions}
        {renderRecentExpenses}
        {renderCashReconciliationStatus}
      </ScrollView>

      {/* Bottom Sheets */}
      <ExpenseBottomSheet ref={expenseBottomSheetRef} />
      <CashReconciliationBottomSheet ref={reconciliationBottomSheetRef} />
      <ProfitLossBottomSheet ref={profitLossBottomSheetRef} data={profitLossData} />
      <PaymentAnalyticsBottomSheet ref={paymentAnalyticsBottomSheetRef} data={paymentAnalytics} />
      <TaxSummaryBottomSheet ref={taxSummaryBottomSheetRef} data={taxSummary} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  periodSelector: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  periodChip: {
    flex: 1,
  },
  overviewCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 12,
  },
  statCardWrapper: {
    width: '48%',
  },
  actionsCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    minWidth: '45%',
  },
  recentCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  expenseItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  expenseInfo: {
    flex: 1,
  },
  reconciliationCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  reconciliationInfo: {
    marginTop: 8,
  },
});

export default FinancialScreen;
