import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { ScrollView, View, StyleSheet, RefreshControl } from 'react-native';
import {
  Text,
  Surface,
  Button,
  Card,
  useTheme,
  Chip,
  ProgressBar,
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useFinancial } from '../context/FinancialContext';
import { FINANCIAL_CONFIG } from '../config/constants';
import CommonHeader from '../components/CommonHeader';
import navigationService from '../services/NavigationService';
import ExpenseBottomSheet from '../components/ExpenseBottomSheet';
import CashReconciliationBottomSheet from '../components/CashReconciliationBottomSheet';
import ProfitLossBottomSheet from '../components/ProfitLossBottomSheet';
import PaymentAnalyticsBottomSheet from '../components/PaymentAnalyticsBottomSheet';
import TaxSummaryBottomSheet from '../components/TaxSummaryBottomSheet';


const FinancialScreen = ({ navigation, navigateToTab }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const {
    expenses,
    reconciliations,
    profitLossData,
    paymentAnalytics,
    taxSummary,
    derivedData,
    loading,
    error,
    loadExpenses,
    loadReconciliations,
    generateProfitLossStatement,
    getPaymentMethodAnalytics,
    getTaxSummary,
  } = useFinancial();

  const [refreshing, setRefreshing] = useState(false);
  const [selectedPeriod, setSelectedPeriod] = useState('month');

  // Bottom sheet refs
  const expenseBottomSheetRef = useRef(null);
  const reconciliationBottomSheetRef = useRef(null);
  const profitLossBottomSheetRef = useRef(null);
  const paymentAnalyticsBottomSheetRef = useRef(null);
  const taxSummaryBottomSheetRef = useRef(null);

  // Helper function to get period dates
  const getPeriodDates = useCallback((period) => {
    const now = new Date();
    let startDate, endDate;

    switch (period) {
      case 'week':
        startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 7);
        endDate = now;
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = now;
        break;
      case 'quarter':
        const quarter = Math.floor(now.getMonth() / 3);
        startDate = new Date(now.getFullYear(), quarter * 3, 1);
        endDate = now;
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        endDate = now;
        break;
      default:
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = now;
    }

    return {
      startDate: startDate.toISOString().split('T')[0],
      endDate: endDate.toISOString().split('T')[0],
    };
  }, []);

  // Memoize period dates to prevent unnecessary recalculations
  const periodDates = useMemo(() => {
    return getPeriodDates(selectedPeriod);
  }, [selectedPeriod, getPeriodDates]);

  // Load data on mount and period change with debouncing
  useEffect(() => {
    let mounted = true;
    let timeoutId;

    const loadData = async () => {
      if (mounted && !loading) {
        // Debounce data loading to prevent rapid successive calls
        timeoutId = setTimeout(async () => {
          if (mounted) {
            await loadInitialData();
          }
        }, 100);
      }
    };

    loadData();

    return () => {
      mounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [selectedPeriod, loading]);

  const loadInitialData = useCallback(async () => {
    try {
      const { startDate, endDate } = periodDates;

      // Load data in batches to improve perceived performance
      // First load critical data
      await Promise.all([
        loadExpenses({ startDate, endDate }),
        generateProfitLossStatement(startDate, endDate),
      ]);

      // Then load secondary data
      await Promise.all([
        loadReconciliations({ startDate, endDate }),
        getPaymentMethodAnalytics(startDate, endDate),
        getTaxSummary(startDate, endDate),
      ]);
    } catch (error) {
      console.error('Failed to load financial data:', error);
    }
  }, [periodDates, loadExpenses, generateProfitLossStatement, loadReconciliations, getPaymentMethodAnalytics, getTaxSummary]);

  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    await loadInitialData();
    setRefreshing(false);
  }, [loadInitialData]);

  const formatCurrency = (amount) => {
    return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}${amount.toFixed(FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES)}`;
  };

  const renderPeriodSelector = () => (
    <View style={styles.periodSelector}>
      {['week', 'month', 'quarter', 'year'].map(period => (
        <Chip
          key={period}
          selected={selectedPeriod === period}
          onPress={() => setSelectedPeriod(period)}
          style={[
            styles.periodChip,
            selectedPeriod === period && { backgroundColor: theme.colors.primary }
          ]}
          textStyle={{
            color: selectedPeriod === period ? theme.colors.onPrimary : theme.colors.onSurface
          }}
        >
          {period.charAt(0).toUpperCase() + period.slice(1)}
        </Chip>
      ))}
    </View>
  );

  // Memoize expensive render functions to prevent unnecessary re-renders
  const renderFinancialOverview = useMemo(() => (
    <Card style={[styles.overviewCard, { backgroundColor: theme.colors.surface }]}>
      <Card.Content>
        <Text variant="titleLarge" style={{ color: theme.colors.onSurface, marginBottom: 16 }}>
          Financial Overview
        </Text>

        <View style={styles.overviewGrid}>
          <View style={styles.overviewItem}>
            <Icon name="trending-up" size={24} color={theme.colors.primary} />
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Revenue
            </Text>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
              {formatCurrency(profitLossData?.revenue?.totalSales || 0)}
            </Text>
          </View>

          <View style={styles.overviewItem}>
            <Icon name="trending-down" size={24} color="#F44336" />
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Expenses
            </Text>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
              {formatCurrency(derivedData.totalExpenses)}
            </Text>
          </View>

          <View style={styles.overviewItem}>
            <Icon name="chart-line" size={24} color="#4CAF50" />
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Net Profit
            </Text>
            <Text variant="titleMedium" style={{
              color: (profitLossData?.profit?.net || 0) >= 0 ? '#4CAF50' : '#F44336',
              fontWeight: '700'
            }}>
              {formatCurrency(profitLossData?.profit?.net || 0)}
            </Text>
          </View>

          <View style={styles.overviewItem}>
            <Icon name="percent" size={24} color="#9C27B0" />
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Profit Margin
            </Text>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
              {(profitLossData?.profit?.margin || 0).toFixed(1)}%
            </Text>
          </View>
        </View>
      </Card.Content>
    </Card>
  ), [theme.colors, profitLossData, derivedData]);

  const renderQuickActions = useMemo(() => (
    <Card style={[styles.actionsCard, { backgroundColor: theme.colors.surface }]}>
      <Card.Content>
        <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginBottom: 16 }}>
          Quick Actions
        </Text>

        <View style={styles.actionsGrid}>
          <Button
            mode="contained-tonal"
            onPress={() => expenseBottomSheetRef.current?.expand()}
            icon="plus"
            style={styles.actionButton}
          >
            Add Expense
          </Button>

          <Button
            mode="contained-tonal"
            onPress={() => reconciliationBottomSheetRef.current?.expand()}
            icon="cash-register"
            style={styles.actionButton}
          >
            Cash Reconciliation
          </Button>

          <Button
            mode="contained-tonal"
            onPress={() => profitLossBottomSheetRef.current?.expand()}
            icon="chart-line"
            style={styles.actionButton}
          >
            P&L Statement
          </Button>

          <Button
            mode="contained-tonal"
            onPress={() => paymentAnalyticsBottomSheetRef.current?.expand()}
            icon="credit-card"
            style={styles.actionButton}
          >
            Payment Analytics
          </Button>

          <Button
            mode="contained-tonal"
            onPress={() => taxSummaryBottomSheetRef.current?.expand()}
            icon="calculator"
            style={styles.actionButton}
          >
            Tax Summary
          </Button>

          <Button
            mode="contained-tonal"
            onPress={() => {
              console.log('Navigating to Advanced Reports...');
              try {
                navigationService.navigate('Reports');
              } catch (error) {
                console.error('Failed to navigate to Reports:', error);
              }
            }}
            icon="file-chart"
            style={styles.actionButton}
          >
            Advanced Reports
          </Button>
        </View>
      </Card.Content>
    </Card>
  ), [theme.colors]);

  const renderRecentExpenses = useMemo(() => (
    <Card style={[styles.recentCard, { backgroundColor: theme.colors.surface }]}>
      <Card.Content>
        <View style={styles.cardHeader}>
          <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
            Recent Expenses
          </Text>
          <Button
            mode="text"
            onPress={() => expenseBottomSheetRef.current?.expand()}
            compact
          >
            View All
          </Button>
        </View>

        {expenses.slice(0, 3).map(expense => (
          <View key={expense.id} style={styles.expenseItem}>
            <View style={styles.expenseInfo}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                {expense.description}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                {expense.category} • {expense.date}
              </Text>
            </View>
            <Text variant="titleSmall" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {formatCurrency(expense.amount)}
            </Text>
          </View>
        ))}

        {expenses.length === 0 && (
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 16 }}>
            No expenses recorded yet
          </Text>
        )}
      </Card.Content>
    </Card>
  ), [theme.colors, expenses]);

  const renderCashReconciliationStatus = useMemo(() => {
    const latestReconciliation = reconciliations[0];

    return (
      <Card style={[styles.reconciliationCard, { backgroundColor: theme.colors.surface }]}>
        <Card.Content>
          <View style={styles.cardHeader}>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurface }}>
              Cash Reconciliation
            </Text>
            <Chip
              icon={latestReconciliation?.status === 'balanced' ? 'check' : 'alert'}
              style={{
                backgroundColor: latestReconciliation?.status === 'balanced' ? '#4CAF50' : '#FF9800'
              }}
              textStyle={{ color: '#FFFFFF' }}
            >
              {latestReconciliation?.status || 'Pending'}
            </Chip>
          </View>

          {latestReconciliation ? (
            <View style={styles.reconciliationInfo}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                Last reconciliation: {latestReconciliation.date}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Difference: {formatCurrency(Math.abs(latestReconciliation.difference))}
              </Text>
            </View>
          ) : (
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              No reconciliation performed today
            </Text>
          )}
        </Card.Content>
      </Card>
    );
  }, [theme.colors, reconciliations]);

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      {/* Header */}
      <CommonHeader
        title="Financial Analytics"
        subtitle="Track your bakery's financial performance"
        showSearch={false}
      />

      <ScrollView
        style={styles.content}
        contentContainerStyle={{ paddingTop: 16 }}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
        showsVerticalScrollIndicator={false}
      >
        {renderPeriodSelector()}
        {renderFinancialOverview}
        {renderQuickActions}
        {renderRecentExpenses}
        {renderCashReconciliationStatus}
      </ScrollView>

      {/* Bottom Sheets */}
      <ExpenseBottomSheet ref={expenseBottomSheetRef} />
      <CashReconciliationBottomSheet ref={reconciliationBottomSheetRef} />
      <ProfitLossBottomSheet ref={profitLossBottomSheetRef} data={profitLossData} />
      <PaymentAnalyticsBottomSheet ref={paymentAnalyticsBottomSheetRef} data={paymentAnalytics} />
      <TaxSummaryBottomSheet ref={taxSummaryBottomSheetRef} data={taxSummary} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  periodSelector: {
    flexDirection: 'row',
    marginBottom: 16,
    gap: 8,
  },
  periodChip: {
    flex: 1,
  },
  overviewCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  overviewGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  overviewItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 16,
  },
  actionsCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  actionButton: {
    flex: 1,
    minWidth: '45%',
  },
  recentCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  expenseItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
  },
  expenseInfo: {
    flex: 1,
  },
  reconciliationCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  reconciliationInfo: {
    marginTop: 8,
  },
});

export default FinancialScreen;
