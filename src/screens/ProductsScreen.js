import React, { useState, useRef, useCallback, useMemo } from 'react';
import { FlatList, ScrollView, View, StyleSheet, Alert, Image, RefreshControl } from 'react-native';
import {
  Card,
  Title,
  Paragraph,
  Text,
  Button,
  Chip,
  Surface,
  IconButton,
  Menu,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import ProductBottomSheet from '../components/ProductBottomSheet';
import CommonHeader from '../components/CommonHeader';
import UnifiedFilterChips from '../components/UnifiedFilterChips';
import UnifiedEmptyState from '../components/UnifiedEmptyState';
import navigationService from '../services/NavigationService';

const ProductsScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');
  const productBottomSheetRef = useRef(null);
  const [editingProduct, setEditingProduct] = useState(null);
  const [menuVisible, setMenuVisible] = useState({});
  const [refreshing, setRefreshing] = useState(false);

  const categories = ['All', 'Bread', 'Pastries', 'Cakes', 'Beverages'];

  // Pull to refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Simulate data refresh - in real app, this would reload from API
      await new Promise(resolve => setTimeout(resolve, 1000));
      console.log('Products data refreshed');
    } catch (error) {
      console.log('Failed to refresh products data:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  const filteredProducts = useMemo(() => {
    return state.products.filter(product => {
      const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesCategory = selectedCategory === 'All' || product.category === selectedCategory;
      return matchesSearch && matchesCategory;
    });
  }, [state.products, searchQuery, selectedCategory]);

  const handleEditProduct = (product) => {
    setEditingProduct(product);
    productBottomSheetRef.current?.expand();
  };

  const handleDeleteProduct = (product) => {
    Alert.alert(
      'Delete Product',
      `Are you sure you want to delete "${product.name}"?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => actions.deleteProduct(product.id),
        },
      ]
    );
  };

  const handleAddProduct = () => {
    setEditingProduct(null);
    productBottomSheetRef.current?.expand();
  };

  const handleAddToOrder = (product) => {
    if (product.stock > 0) {
      // This would typically navigate to order creation or add to cart
      Alert.alert('Add to Order', `${product.name} would be added to a new order`);
    } else {
      Alert.alert('Out of Stock', 'This product is currently out of stock');
    }
  };

  const toggleMenu = (productId) => {
    setMenuVisible(prev => ({
      ...prev,
      [productId]: !prev[productId]
    }));
  };

  const closeMenu = (productId) => {
    setMenuVisible(prev => ({
      ...prev,
      [productId]: false
    }));
  };

  const handleSaveProduct = (productData) => {
    if (editingProduct) {
      actions.updateProduct(productData);
    } else {
      actions.addProduct(productData);
    }
    setEditingProduct(null);
  };

  const renderProductCard = useCallback(({ item: product }) => (
    <Surface style={[styles.productCard, { backgroundColor: theme.colors.surface }]} elevation={0}>
      {product.image && (
        <Image source={{ uri: product.image }} style={styles.productImage} />
      )}
      <View style={styles.productContent}>
        <View style={styles.productHeader}>
          <View style={[styles.iconContainer, { backgroundColor: theme.colors.primary + '15' }]}>
            <Icon name={product.icon} size={24} color={theme.colors.primary} />
          </View>
          <View style={styles.productInfo}>
            <Text variant="titleMedium" style={{ fontWeight: '600' }}>{product.name}</Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
              {product.description}
            </Text>
            <View style={styles.productMeta}>
              <Text variant="titleLarge" style={{ color: theme.colors.onSurface, fontWeight: '700', marginTop: 8 }}>
                ${product.price.toFixed(2)}
              </Text>
              <View style={styles.stockContainer}>
                <View
                  style={[
                    styles.stockDot,
                    {
                      backgroundColor: product.stock > 10 ? theme.colors.tertiary :
                                      product.stock > 5 ? theme.colors.secondary :
                                      theme.colors.error
                    }
                  ]}
                />
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 6 }}>
                  {product.stock} in stock
                </Text>
              </View>
            </View>
          </View>
        </View>

        <View style={styles.actionButtons}>
          <Menu
            visible={menuVisible[product.id] || false}
            onDismiss={() => closeMenu(product.id)}
            anchor={
              <IconButton
                icon="dots-vertical"
                size={20}
                iconColor={theme.colors.onSurfaceVariant}
                onPress={() => toggleMenu(product.id)}
              />
            }
          >
            <Menu.Item
              onPress={() => {
                closeMenu(product.id);
                handleEditProduct(product);
              }}
              title="Edit"
              leadingIcon="pencil"
            />
            <Menu.Item
              onPress={() => {
                closeMenu(product.id);
                handleDeleteProduct(product);
              }}
              title="Delete"
              leadingIcon="delete"
            />
          </Menu>

          <IconButton
            icon="plus"
            size={24}
            iconColor={product.stock > 0 ? theme.colors.primary : theme.colors.onSurfaceVariant}
            style={[
              styles.addButton,
              {
                backgroundColor: product.stock > 0 ? theme.colors.primary + '15' : theme.colors.surfaceVariant
              }
            ]}
            onPress={() => handleAddToOrder(product)}
            disabled={product.stock === 0}
          />
        </View>
      </View>
    </Surface>
  ), [theme, handleEditProduct, handleDeleteProduct, handleAddToOrder, menuVisible, toggleMenu, closeMenu]);



  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Products"
        subtitle="Manage your inventory"
        searchPlaceholder="Search products..."
        searchType="products"
        searchData={state.products}
        searchFields={["name", "description", "category"]}
        onSearchChange={setSearchQuery}
        onSearchResult={(product) => {
          console.log('Product selected from search:', product);
          setEditingProduct(product);
          productBottomSheetRef.current?.expand();
        }}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          console.log('Profile pressed - navigating to MyProfile');
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <FlatList
        data={filteredProducts}
        renderItem={renderProductCard}
        keyExtractor={(item) => item.id.toString()}
        style={styles.content}
        showsVerticalScrollIndicator={false}
        removeClippedSubviews={true}
        maxToRenderPerBatch={10}
        updateCellsBatchingPeriod={50}
        initialNumToRender={8}
        windowSize={10}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
        ListHeaderComponent={() => (
          <View>
            {/* Category Filter */}
            <UnifiedFilterChips
              filters={categories}
              selectedFilter={selectedCategory}
              onFilterChange={setSelectedCategory}
              showCounts={true}
              data={state.products}
              countField="category"
              style={{ marginBottom: 16 }}
            />

            {/* Stats Row */}
            <View style={styles.statsRow}>
              <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
                <Text variant="headlineSmall" style={{ color: theme.colors.primary }}>
                  {filteredProducts.length}
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Products
                </Text>
              </Surface>

              <Surface style={[styles.statCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
                <Text variant="headlineSmall" style={{ color: theme.colors.secondary }}>
                  {filteredProducts.reduce((sum, product) => sum + product.stock, 0)}
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Total Stock
                </Text>
              </Surface>
            </View>
          </View>
        )}
        ListEmptyComponent={() => (
          <UnifiedEmptyState
            type="products"
            searchQuery={searchQuery}
            onActionPress={handleAddProduct}
          />
        )}
      />

      <ProductBottomSheet
        bottomSheetRef={productBottomSheetRef}
        onSave={handleSaveProduct}
        product={editingProduct}
        mode={editingProduct ? 'edit' : 'add'}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchbar: {
    borderRadius: 25,
    height: 48,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  categoryContainer: {
    marginBottom: 8,
  },
  categoryChip: {
    marginRight: 6,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCard: {
    flex: 1,
    padding: 16,
    marginHorizontal: 4,
    borderRadius: 12,
    alignItems: 'center',
  },
  productCard: {
    marginBottom: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
    overflow: 'hidden',
  },
  productImage: {
    width: '100%',
    height: 150,
    resizeMode: 'cover',
  },
  productContent: {
    padding: 16,
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  productInfo: {
    flex: 1,
  },
  productMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  stockDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  addButton: {
    borderRadius: 12,
    width: 48,
    height: 48,
  },

});

export default ProductsScreen;
