import React, { useState, useEffect } from 'react';
import { ScrollView, View, StyleSheet, Alert, FlatList } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  TextInput,
  Button,
  Surface,
  Chip,
  IconButton,
  Card,
  Divider,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import ImagePicker from '../components/ImagePicker';
import navigationService from '../services/NavigationService';

const AddOrderScreen = ({ route }) => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const navigation = useNavigation();

  // Get order from route params for editing
  const editingOrder = route?.params?.order;
  const isEditing = !!editingOrder;

  // Order form state
  const [orderData, setOrderData] = useState({
    customerName: editingOrder?.customerName || editingOrder?.customer || '',
    customerPhone: editingOrder?.phone || '',
    customerEmail: editingOrder?.email || '',
    orderType: editingOrder?.orderType || 'dine-in',
    items: editingOrder?.items || [],
    notes: editingOrder?.notes || '',
    discount: editingOrder?.discount || 0,
    tax: editingOrder?.tax || 0,
    image: editingOrder?.image || null,
  });

  const [selectedProducts, setSelectedProducts] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [errors, setErrors] = useState({});

  // Filter products based on search
  const filteredProducts = state.products.filter(product =>
    product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    product.category.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleInputChange = (field, value) => {
    setOrderData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const addProductToOrder = (product) => {
    const existingItem = orderData.items.find(item => item.productId === product.id);

    if (existingItem) {
      // Increase quantity
      setOrderData(prev => ({
        ...prev,
        items: prev.items.map(item =>
          item.productId === product.id
            ? { ...item, quantity: item.quantity + 1 }
            : item
        )
      }));
    } else {
      // Add new item
      setOrderData(prev => ({
        ...prev,
        items: [...prev.items, {
          productId: product.id,
          productName: product.name,
          price: product.price,
          quantity: 1,
        }]
      }));
    }
  };

  const updateItemQuantity = (productId, quantity) => {
    if (quantity <= 0) {
      removeItemFromOrder(productId);
      return;
    }

    setOrderData(prev => ({
      ...prev,
      items: prev.items.map(item =>
        item.productId === productId
          ? { ...item, quantity }
          : item
      )
    }));
  };

  const removeItemFromOrder = (productId) => {
    setOrderData(prev => ({
      ...prev,
      items: prev.items.filter(item => item.productId !== productId)
    }));
  };

  const calculateSubtotal = () => {
    return orderData.items.reduce((total, item) => total + (item.price * item.quantity), 0);
  };

  const calculateTotal = () => {
    const subtotal = calculateSubtotal();
    const discountAmount = (subtotal * orderData.discount) / 100;
    const taxAmount = ((subtotal - discountAmount) * orderData.tax) / 100;
    return subtotal - discountAmount + taxAmount;
  };

  const validateForm = () => {
    const newErrors = {};

    if (!orderData.customerName.trim()) {
      newErrors.customerName = 'Customer name is required';
    }

    if (orderData.items.length === 0) {
      newErrors.items = 'At least one item is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors and try again.');
      return;
    }

    try {
      const orderToSave = {
        customer: orderData.customerName,
        customerName: orderData.customerName,
        phone: orderData.customerPhone,
        email: orderData.customerEmail,
        items: orderData.items,
        notes: orderData.notes,
        discount: orderData.discount,
        tax: orderData.tax,
        image: orderData.image,
        orderType: orderData.orderType,
        subtotal: calculateSubtotal(),
        total: calculateTotal(),
        updatedAt: new Date().toISOString(),
      };

      if (isEditing) {
        // Update existing order
        const updatedOrder = {
          ...editingOrder,
          ...orderToSave,
        };
        actions.updateOrder(updatedOrder);

        Alert.alert(
          'Success',
          'Order updated successfully!',
          [
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      } else {
        // Add new order
        const newOrder = {
          id: Date.now().toString(),
          ...orderToSave,
          status: 'Pending',
          date: new Date().toISOString().split('T')[0],
          time: new Date().toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          }),
          createdAt: new Date().toISOString(),
        };

        actions.addOrder(newOrder);

        Alert.alert(
          'Success',
          'Order created successfully!',
          [
            {
              text: 'Create Another',
              onPress: () => {
                setOrderData({
                  customerName: '',
                  customerPhone: '',
                  customerEmail: '',
                  orderType: 'dine-in',
                  items: [],
                  notes: '',
                  discount: 0,
                  tax: 0,
                  image: null,
                });
              }
            },
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'create'} order. Please try again.`);
    }
  };

  const renderProductItem = ({ item: product }) => (
    <Card style={styles.productCard} onPress={() => addProductToOrder(product)}>
      <Card.Content style={styles.productContent}>
        <View style={styles.productInfo}>
          <Text variant="titleMedium" style={{ fontWeight: '600' }}>
            {product.name}
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            {product.category}
          </Text>
          <Text variant="titleSmall" style={{ color: theme.colors.primary, fontWeight: '600' }}>
            ৳{product.price.toFixed(2)}
          </Text>
        </View>
        <IconButton
          icon="plus"
          size={20}
          iconColor={theme.colors.primary}
          style={{ backgroundColor: theme.colors.primaryContainer }}
          onPress={() => addProductToOrder(product)}
        />
      </Card.Content>
    </Card>
  );

  const renderOrderItem = (item) => (
    <View key={item.productId} style={styles.orderItem}>
      <View style={styles.orderItemInfo}>
        <Text variant="titleMedium" style={{ fontWeight: '600' }}>
          {item.productName}
        </Text>
        <Text variant="bodySmall" style={{ color: theme.colors.primary }}>
          ৳{item.price.toFixed(2)} each
        </Text>
      </View>

      <View style={styles.quantityControls}>
        <IconButton
          icon="minus"
          size={16}
          onPress={() => updateItemQuantity(item.productId, item.quantity - 1)}
        />
        <Text variant="titleMedium" style={styles.quantity}>
          {item.quantity}
        </Text>
        <IconButton
          icon="plus"
          size={16}
          onPress={() => updateItemQuantity(item.productId, item.quantity + 1)}
        />
      </View>

      <View style={styles.itemTotal}>
        <Text variant="titleMedium" style={{ fontWeight: '600' }}>
          ৳{(item.price * item.quantity).toFixed(2)}
        </Text>
        <IconButton
          icon="delete"
          size={16}
          iconColor={theme.colors.error}
          onPress={() => removeItemFromOrder(item.productId)}
        />
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title={isEditing ? "Edit Order" : "Create Order"}
        subtitle={isEditing ? "Update order details" : "New customer order"}
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          console.log('Profile pressed - navigating to MyProfile');
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>

        {/* Customer Information */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Customer Information
          </Text>

          <TextInput
            mode="outlined"
            label="Customer Name *"
            value={orderData.customerName}
            onChangeText={(value) => handleInputChange('customerName', value)}
            error={!!errors.customerName}
            style={styles.input}
          />
          {errors.customerName && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.customerName}</Text>}

          <View style={styles.row}>
            <TextInput
              mode="outlined"
              label="Phone"
              value={orderData.customerPhone}
              onChangeText={(value) => handleInputChange('customerPhone', value)}
              keyboardType="phone-pad"
              style={[styles.input, styles.halfWidth]}
            />

            <TextInput
              mode="outlined"
              label="Email"
              value={orderData.customerEmail}
              onChangeText={(value) => handleInputChange('customerEmail', value)}
              keyboardType="email-address"
              style={[styles.input, styles.halfWidth]}
            />
          </View>
        </Surface>

        {/* Order Type */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Order Type
          </Text>

          <View style={styles.chipContainer}>
            {[
              { id: 'dine-in', label: 'Dine In', icon: 'silverware-fork-knife' },
              { id: 'takeaway', label: 'Takeaway', icon: 'bag-personal' },
              { id: 'delivery', label: 'Delivery', icon: 'truck-delivery' }
            ].map((type) => (
              <Chip
                key={type.id}
                selected={orderData.orderType === type.id}
                onPress={() => handleInputChange('orderType', type.id)}
                icon={type.icon}
                style={[
                  styles.chip,
                  orderData.orderType === type.id && { backgroundColor: theme.colors.primaryContainer }
                ]}
                textStyle={{
                  color: orderData.orderType === type.id ? theme.colors.onPrimaryContainer : theme.colors.onSurface
                }}
              >
                {type.label}
              </Chip>
            ))}
          </View>
        </Surface>

        {/* Add Products */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Add Products
          </Text>

          <TextInput
            mode="outlined"
            label="Search products..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            left={<TextInput.Icon icon="magnify" />}
            style={styles.input}
          />

          <FlatList
            data={filteredProducts}
            renderItem={renderProductItem}
            keyExtractor={(item) => item.id}
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.productsList}
          />
        </Surface>

        {/* Order Items */}
        {orderData.items.length > 0 && (
          <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
            <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
              Order Items ({orderData.items.length})
            </Text>
            {errors.items && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.items}</Text>}

            {orderData.items.map(renderOrderItem)}

            <Divider style={{ marginVertical: 16 }} />

            {/* Order Summary */}
            <View style={styles.summaryRow}>
              <Text variant="bodyLarge">Subtotal:</Text>
              <Text variant="bodyLarge">৳{calculateSubtotal().toFixed(2)}</Text>
            </View>

            <View style={styles.row}>
              <TextInput
                mode="outlined"
                label="Discount %"
                value={orderData.discount.toString()}
                onChangeText={(value) => handleInputChange('discount', parseFloat(value) || 0)}
                keyboardType="decimal-pad"
                style={[styles.input, styles.halfWidth]}
              />

              <TextInput
                mode="outlined"
                label="Tax %"
                value={orderData.tax.toString()}
                onChangeText={(value) => handleInputChange('tax', parseFloat(value) || 0)}
                keyboardType="decimal-pad"
                style={[styles.input, styles.halfWidth]}
              />
            </View>

            <View style={[styles.summaryRow, styles.totalRow]}>
              <Text variant="headlineSmall" style={{ fontWeight: '700' }}>Total:</Text>
              <Text variant="headlineSmall" style={{ fontWeight: '700', color: theme.colors.primary }}>
                ৳{calculateTotal().toFixed(2)}
              </Text>
            </View>
          </Surface>
        )}

        {/* Notes */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Order Notes
          </Text>

          <TextInput
            mode="outlined"
            label="Special instructions..."
            value={orderData.notes}
            onChangeText={(value) => handleInputChange('notes', value)}
            multiline
            numberOfLines={3}
            style={styles.input}
          />
        </Surface>

        {/* Compact Image Section */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Order Image
          </Text>
          <View style={styles.compactImageContainer}>
            <ImagePicker
              key={orderData.image || 'no-image'} // Force re-render when image changes
              onImageSelected={(imageUri) => handleInputChange('image', imageUri)}
              currentImage={orderData.image}
              placeholder="Add image (optional)"
            />
          </View>
        </Surface>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={[styles.button, styles.cancelButton]}
            labelStyle={{ color: theme.colors.onSurfaceVariant }}
          >
            Cancel
          </Button>

          <Button
            mode="contained"
            onPress={handleSave}
            style={[styles.button, styles.saveButton]}
            buttonColor={theme.colors.primary}
          >
            {isEditing ? 'Update Order' : 'Create Order'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  errorText: {
    fontSize: 12,
    marginTop: -8,
    marginBottom: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    marginBottom: 8,
  },
  productsList: {
    maxHeight: 120,
  },
  productCard: {
    width: 200,
    marginRight: 12,
  },
  productContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  productInfo: {
    flex: 1,
  },
  orderItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#E0E0E0',
  },
  orderItemInfo: {
    flex: 1,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantity: {
    marginHorizontal: 8,
    minWidth: 30,
    textAlign: 'center',
  },
  itemTotal: {
    alignItems: 'flex-end',
    minWidth: 80,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  totalRow: {
    paddingTop: 12,
    borderTopWidth: 2,
    borderTopColor: '#E0E0E0',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
    marginBottom: 32,
  },
  button: {
    flex: 1,
  },
  cancelButton: {
    borderColor: '#E0E0E0',
  },
  saveButton: {
    elevation: 2,
  },
  compactImageContainer: {
    alignItems: 'center',
  },
});

export default AddOrderScreen;
