import React, { useState } from 'react';
import { ScrollView, View, StyleSheet, Alert, Image } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  TextInput,
  Button,
  Surface,
  Switch,
  Chip,
  IconButton,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import ImagePicker from '../components/ImagePicker';

const AddProductScreen = ({ route }) => {
  const { theme } = useTheme();
  const { actions } = useData();
  const navigation = useNavigation();

  // Get product from route params for editing
  const editingProduct = route?.params?.product;
  const isEditing = !!editingProduct;

  // Product form state
  const [productData, setProductData] = useState({
    name: editingProduct?.name || '',
    description: editingProduct?.description || '',
    price: editingProduct?.price?.toString() || '',
    cost: editingProduct?.cost?.toString() || '',
    category: editingProduct?.category || '',
    stock: editingProduct?.stock?.toString() || '',
    sku: editingProduct?.sku || '',
    barcode: editingProduct?.barcode || '',
    image: editingProduct?.image || null,
    isActive: editingProduct?.isActive ?? true,
    isFeatured: editingProduct?.isFeatured ?? false,
    tags: editingProduct?.tags || [],
  });

  const [newTag, setNewTag] = useState('');
  const [errors, setErrors] = useState({});

  // Auto-generate SKU and Barcode when product name changes (only for new products)
  React.useEffect(() => {
    if (!isEditing && productData.name && !productData.sku) {
      const generateSKU = () => {
        const prefix = productData.category ? productData.category.substring(0, 3).toUpperCase() : 'PRD';
        const timestamp = Date.now().toString().slice(-6);
        const nameCode = productData.name.replace(/[^a-zA-Z0-9]/g, '').substring(0, 3).toUpperCase();
        return `${prefix}-${nameCode}-${timestamp}`;
      };

      const generateBarcode = () => {
        // Generate a 13-digit EAN-13 barcode
        const timestamp = Date.now().toString();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${timestamp.slice(-7)}${random}000`;
      };

      setProductData(prev => ({
        ...prev,
        sku: generateSKU(),
        barcode: generateBarcode()
      }));
    }
  }, [productData.name, productData.category, isEditing]);

  // Predefined categories
  const categories = [
    'Cakes', 'Pastries', 'Bread', 'Cookies', 'Beverages', 'Desserts', 'Seasonal'
  ];

  const handleInputChange = (field, value) => {
    setProductData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: null
      }));
    }
  };

  const addTag = () => {
    if (newTag.trim() && !productData.tags.includes(newTag.trim())) {
      setProductData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove) => {
    setProductData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!productData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!productData.price || isNaN(productData.price) || parseFloat(productData.price) <= 0) {
      newErrors.price = 'Valid price is required';
    }

    if (!productData.category) {
      newErrors.category = 'Category is required';
    }

    if (!productData.stock || isNaN(productData.stock) || parseInt(productData.stock) < 0) {
      newErrors.stock = 'Valid stock quantity is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors and try again.');
      return;
    }

    try {
      const productToSave = {
        ...productData,
        price: parseFloat(productData.price),
        cost: productData.cost ? parseFloat(productData.cost) : 0,
        stock: parseInt(productData.stock),
        updatedAt: new Date().toISOString(),
      };

      if (isEditing) {
        // Update existing product
        const updatedProduct = {
          ...editingProduct,
          ...productToSave,
        };
        actions.updateProduct(updatedProduct);

        Alert.alert(
          'Success',
          'Product updated successfully!',
          [
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      } else {
        // Add new product
        const newProduct = {
          id: Date.now().toString(),
          ...productToSave,
          createdAt: new Date().toISOString(),
        };

        actions.addProduct(newProduct);

        Alert.alert(
          'Success',
          'Product added successfully!',
          [
            {
              text: 'Add Another',
              onPress: () => {
                setProductData({
                  name: '',
                  description: '',
                  price: '',
                  cost: '',
                  category: '',
                  stock: '',
                  sku: '',
                  barcode: '',
                  image: null,
                  isActive: true,
                  isFeatured: false,
                  tags: [],
                });
              }
            },
            {
              text: 'Done',
              onPress: () => navigation.goBack(),
              style: 'default'
            }
          ]
        );
      }
    } catch (error) {
      Alert.alert('Error', `Failed to ${isEditing ? 'update' : 'add'} product. Please try again.`);
    }
  };

  const renderCategoryChips = () => (
    <View style={styles.chipContainer}>
      {categories.map((category) => (
        <Chip
          key={category}
          selected={productData.category === category}
          onPress={() => handleInputChange('category', category)}
          style={[
            styles.chip,
            productData.category === category && { backgroundColor: theme.colors.primaryContainer }
          ]}
          textStyle={{
            color: productData.category === category ? theme.colors.onPrimaryContainer : theme.colors.onSurface
          }}
        >
          {category}
        </Chip>
      ))}
    </View>
  );

  const renderTagSection = () => (
    <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
        Tags
      </Text>

      <View style={styles.tagInputContainer}>
        <TextInput
          mode="outlined"
          label="Add tag"
          value={newTag}
          onChangeText={setNewTag}
          onSubmitEditing={addTag}
          style={styles.tagInput}
          right={
            <TextInput.Icon
              icon="plus"
              onPress={addTag}
              disabled={!newTag.trim()}
            />
          }
        />
      </View>

      <View style={styles.tagsContainer}>
        {productData.tags.map((tag, index) => (
          <Chip
            key={index}
            onClose={() => removeTag(tag)}
            style={styles.tag}
          >
            {tag}
          </Chip>
        ))}
      </View>
    </Surface>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title={isEditing ? "Edit Product" : "Add Product"}
        subtitle={isEditing ? "Update product details" : "Create a new product"}
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          const rootNavigation = navigation.getParent('AppNavigator') || navigation.getParent();
          if (rootNavigation) {
            rootNavigation.navigate('MyProfile');
          }
        }}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>

        {/* Basic Information */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Basic Information
          </Text>

          <TextInput
            mode="outlined"
            label="Product Name *"
            value={productData.name}
            onChangeText={(value) => handleInputChange('name', value)}
            error={!!errors.name}
            style={styles.input}
          />
          {errors.name && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.name}</Text>}

          <TextInput
            mode="outlined"
            label="Description"
            value={productData.description}
            onChangeText={(value) => handleInputChange('description', value)}
            multiline
            numberOfLines={3}
            style={styles.input}
          />

          <View style={styles.row}>
            <TextInput
              mode="outlined"
              label="Price *"
              value={productData.price}
              onChangeText={(value) => handleInputChange('price', value)}
              keyboardType="decimal-pad"
              error={!!errors.price}
              style={[styles.input, styles.halfWidth]}
              left={<TextInput.Icon icon="currency-bdt" />}
            />

            <TextInput
              mode="outlined"
              label="Cost"
              value={productData.cost}
              onChangeText={(value) => handleInputChange('cost', value)}
              keyboardType="decimal-pad"
              style={[styles.input, styles.halfWidth]}
              left={<TextInput.Icon icon="currency-bdt" />}
            />
          </View>
          {errors.price && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.price}</Text>}
        </Surface>

        {/* Category */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Category *
          </Text>
          {renderCategoryChips()}
          {errors.category && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.category}</Text>}
        </Surface>

        {/* Inventory */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Inventory
          </Text>

          <View style={styles.row}>
            <TextInput
              mode="outlined"
              label="Stock Quantity *"
              value={productData.stock}
              onChangeText={(value) => handleInputChange('stock', value)}
              keyboardType="number-pad"
              error={!!errors.stock}
              style={[styles.input, styles.halfWidth]}
            />

            <TextInput
              mode="outlined"
              label="SKU"
              value={productData.sku}
              onChangeText={(value) => handleInputChange('sku', value)}
              style={[styles.input, styles.halfWidth]}
            />
          </View>
          {errors.stock && <Text style={[styles.errorText, { color: theme.colors.error }]}>{errors.stock}</Text>}

          <TextInput
            mode="outlined"
            label="Barcode"
            value={productData.barcode}
            onChangeText={(value) => handleInputChange('barcode', value)}
            style={styles.input}
          />
        </Surface>

        {/* Settings */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Settings
          </Text>

          <View style={styles.switchRow}>
            <View style={styles.switchLabel}>
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurface }}>Active Product</Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Available for sale
              </Text>
            </View>
            <Switch
              value={productData.isActive}
              onValueChange={(value) => handleInputChange('isActive', value)}
              color={theme.colors.primary}
            />
          </View>

          <View style={styles.switchRow}>
            <View style={styles.switchLabel}>
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurface }}>Featured Product</Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                Highlight in recommendations
              </Text>
            </View>
            <Switch
              value={productData.isFeatured}
              onValueChange={(value) => handleInputChange('isFeatured', value)}
              color={theme.colors.primary}
            />
          </View>
        </Surface>

        {/* Tags */}
        {renderTagSection()}

        {/* Compact Image Section */}
        <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Product Image
          </Text>
          <View style={styles.compactImageContainer}>
            <ImagePicker
              onImageSelected={(imageUri) => handleInputChange('image', imageUri)}
              currentImage={productData.image}
              placeholder="Add image"
            />
          </View>
        </Surface>

        {/* Action Buttons */}
        <View style={styles.buttonContainer}>
          <Button
            mode="outlined"
            onPress={() => navigation.goBack()}
            style={[styles.button, styles.cancelButton]}
            labelStyle={{ color: theme.colors.onSurfaceVariant }}
          >
            Cancel
          </Button>

          <Button
            mode="contained"
            onPress={handleSave}
            style={[styles.button, styles.saveButton]}
            buttonColor={theme.colors.primary}
          >
            {isEditing ? 'Update Product' : 'Save Product'}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 16,
    borderRadius: 12,
    padding: 16,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  halfWidth: {
    flex: 1,
  },
  errorText: {
    fontSize: 12,
    marginTop: -8,
    marginBottom: 8,
  },
  chipContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    marginBottom: 8,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  switchLabel: {
    flex: 1,
  },
  tagInputContainer: {
    marginBottom: 12,
  },
  tagInput: {
    flex: 1,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  tag: {
    marginBottom: 4,
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 24,
    marginBottom: 32,
  },
  button: {
    flex: 1,
  },
  cancelButton: {
    borderColor: '#E0E0E0',
  },
  saveButton: {
    elevation: 2,
  },
  compactImageContainer: {
    alignItems: 'center',
  },
});

export default AddProductScreen;
