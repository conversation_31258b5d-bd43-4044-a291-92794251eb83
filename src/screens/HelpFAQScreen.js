import React, { useState } from 'react';
import { ScrollView, View, StyleSheet } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Text,
  Surface,
  List,
  Searchbar,
  Chip,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../context/ThemeContext';
import CommonHeader from '../components/CommonHeader';
import { SPACING, BORDER_RADIUS, TYPOGRAPHY } from '../theme/designTokens';

const HelpFAQScreen = () => {
  const { theme } = useTheme();
  const navigation = useNavigation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = [
    { id: 'all', label: 'All', icon: 'help-circle' },
    { id: 'orders', label: 'Orders', icon: 'clipboard-list' },
    { id: 'products', label: 'Products', icon: 'food-croissant' },
    { id: 'financial', label: 'Financial', icon: 'chart-line' },
    { id: 'settings', label: 'Settings', icon: 'cog' },
  ];

  const faqData = [
    {
      id: 1,
      category: 'orders',
      question: 'How do I create a new order?',
      answer: 'To create a new order, tap the plus (+) button in the bottom navigation and select "Add Order". Fill in customer details, add products, and save.',
    },
    {
      id: 2,
      category: 'orders',
      question: 'How can I edit an existing order?',
      answer: 'Go to the Orders tab, find your order, and tap on it. Then tap the edit button to modify order details.',
    },
    {
      id: 3,
      category: 'products',
      question: 'How do I add a new product?',
      answer: 'Tap the plus (+) button and select "Add Product". Enter product details like name, price, description, and stock quantity.',
    },
    {
      id: 4,
      category: 'products',
      question: 'How do I manage product inventory?',
      answer: 'Go to the Products tab to view all products. You can edit stock quantities, update prices, and manage product details.',
    },
    {
      id: 5,
      category: 'financial',
      question: 'How do I view financial reports?',
      answer: 'Tap on "View Reports" from the dashboard or navigate to the Financial tab to see detailed profit/loss statements and analytics.',
    },
    {
      id: 6,
      category: 'financial',
      question: 'How do I export financial data?',
      answer: 'In the Financial section, you can export data as PDF or Excel files using the export buttons at the bottom of reports.',
    },
    {
      id: 7,
      category: 'settings',
      question: 'How do I backup my data?',
      answer: 'Go to Settings > Data Backup to export all your business data. You can also import previously exported data.',
    },
    {
      id: 8,
      category: 'settings',
      question: 'How do I change my profile information?',
      answer: 'Tap your profile picture in the top-right corner, then select "Edit Profile" to update your business information.',
    },
    {
      id: 9,
      category: 'orders',
      question: 'How do I generate invoices?',
      answer: 'In the Orders tab, tap on any order and select "Generate Invoice" to create a PDF invoice for your customer.',
    },
    {
      id: 10,
      category: 'products',
      question: 'Can I add product images?',
      answer: 'Yes! When adding or editing a product, you can upload images using the camera or gallery through the image picker.',
    },
  ];

  const filteredFAQs = faqData.filter(faq => {
    const matchesCategory = selectedCategory === 'all' || faq.category === selectedCategory;
    const matchesSearch = faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         faq.answer.toLowerCase().includes(searchQuery.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Help & FAQ"
        subtitle="Find answers to common questions"
        showSearch={false}
        showBackButton={true}
        onBackPress={() => navigation.goBack()}
      />

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Search Bar */}
        <Searchbar
          placeholder="Search help topics..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={[styles.searchBar, { backgroundColor: theme.colors.surfaceVariant }]}
          iconColor={theme.colors.onSurfaceVariant}
          inputStyle={{ color: theme.colors.onSurface }}
        />

        {/* Category Filters */}
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoriesContainer}>
          {categories.map((category) => (
            <Chip
              key={category.id}
              selected={selectedCategory === category.id}
              onPress={() => setSelectedCategory(category.id)}
              icon={category.icon}
              style={[
                styles.categoryChip,
                selectedCategory === category.id && { backgroundColor: theme.colors.primary }
              ]}
              textStyle={{
                color: selectedCategory === category.id ? theme.colors.onPrimary : theme.colors.onSurface
              }}
            >
              {category.label}
            </Chip>
          ))}
        </ScrollView>

        {/* FAQ List */}
        <Surface style={[styles.faqContainer, { backgroundColor: theme.colors.surface }]} elevation={1}>
          {filteredFAQs.map((faq) => (
            <List.Accordion
              key={faq.id}
              title={faq.question}
              titleStyle={{
                color: theme.colors.onSurface,
                fontSize: TYPOGRAPHY.cardTitle.fontSize,
                fontWeight: '600'
              }}
              left={props => (
                <Icon
                  name="help-circle-outline"
                  size={24}
                  color={theme.colors.primary}
                  style={{ marginLeft: 8 }}
                />
              )}
              style={styles.accordionItem}
            >
              <View style={styles.answerContainer}>
                <Text
                  variant="bodyMedium"
                  style={{
                    color: theme.colors.onSurfaceVariant,
                    lineHeight: 22,
                    paddingHorizontal: 16,
                    paddingBottom: 16
                  }}
                >
                  {faq.answer}
                </Text>
              </View>
            </List.Accordion>
          ))}
        </Surface>

        {filteredFAQs.length === 0 && (
          <View style={styles.emptyState}>
            <Icon name="help-circle-outline" size={64} color={theme.colors.onSurfaceVariant} />
            <Text variant="titleMedium" style={{ color: theme.colors.onSurfaceVariant, marginTop: 16 }}>
              No results found
            </Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginTop: 8, textAlign: 'center' }}>
              Try adjusting your search or category filter
            </Text>
          </View>
        )}

        {/* Contact Support Section */}
        <Surface style={[styles.supportSection, { backgroundColor: theme.colors.primaryContainer }]} elevation={1}>
          <View style={styles.supportContent}>
            <Icon name="headset" size={32} color={theme.colors.onPrimaryContainer} />
            <View style={styles.supportText}>
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                Still need help?
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer, marginTop: 4 }}>
                Contact our support team for personalized assistance
              </Text>
            </View>
          </View>
        </Surface>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: SPACING.lg,
  },
  searchBar: {
    marginBottom: SPACING.md,
    borderRadius: BORDER_RADIUS.lg,
  },
  categoriesContainer: {
    marginBottom: SPACING.lg,
  },
  categoryChip: {
    marginRight: SPACING.sm,
    marginBottom: SPACING.xs,
  },
  faqContainer: {
    borderRadius: BORDER_RADIUS.xl,
    marginBottom: SPACING.lg,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  accordionItem: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  answerContainer: {
    backgroundColor: 'rgba(0,0,0,0.02)',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xl * 2,
  },
  supportSection: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.lg,
  },
  supportContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  supportText: {
    flex: 1,
    marginLeft: SPACING.md,
  },
});

export default HelpFAQScreen;
