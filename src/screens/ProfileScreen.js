import React, { useState, useRef } from 'react';
import { ScrollView, View, StyleSheet, Alert } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import {
  Card,
  Text,
  Button,
  Surface,
  Avatar,
  List,
  Divider,
  Switch,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import EditProfileBottomSheet from '../components/EditProfileBottomSheet';
import StoreHoursModal from '../components/StoreHoursModal';
import PaymentMethodsModal from '../components/PaymentMethodsModal';
import CommonHeader from '../components/CommonHeader';

const MyProfileScreen = () => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const navigation = useNavigation();

  // Bottom sheet refs
  const editProfileBottomSheetRef = useRef(null);

  // Modal states
  const [storeHoursModalVisible, setStoreHoursModalVisible] = useState(false);
  const [paymentMethodsModalVisible, setPaymentMethodsModalVisible] = useState(false);

  // Profile-related settings state
  const [notifications, setNotifications] = useState(state.settings.notifications || true);
  const [autoBackup, setAutoBackup] = useState(state.settings.autoBackup || true);

  const handleProfileSave = (profileData) => {
    actions.updateSettings(profileData);
    Alert.alert('Success', 'Profile updated successfully!');
  };

  const handleStoreHoursSave = (storeHours) => {
    actions.updateSettings({ storeHours });
    Alert.alert('Success', 'Store hours updated successfully!');
  };

  const handlePaymentMethodsSave = (paymentMethods) => {
    actions.updateSettings({ paymentMethods });
    Alert.alert('Success', 'Payment methods updated successfully!');
  };

  const handleSettingChange = (key, value) => {
    const newSettings = { [key]: value };
    actions.updateSettings(newSettings);

    // Update local state
    if (key === 'notifications') setNotifications(value);
    if (key === 'autoBackup') setAutoBackup(value);
  };

  const ProfileHeader = () => (
    <Surface style={[styles.profileCard, { backgroundColor: theme.colors.surface }]} elevation={2}>
      <View style={styles.profileContent}>
        <View style={styles.profileHeader}>
          <View style={[styles.avatarContainer, { backgroundColor: theme.colors.primary + '15' }]}>
            <Avatar.Text
              size={80}
              label={state.settings.storeName.split(' ').map(word => word[0]).join('').substring(0, 2)}
              style={{ backgroundColor: theme.colors.primary }}
            />
            <View style={[styles.statusIndicator, { backgroundColor: '#4CAF50' }]} />
          </View>
          <View style={styles.profileInfo}>
            <Text variant="headlineMedium" style={{ fontWeight: '700', color: theme.colors.onSurface }}>
              {state.settings.storeName}
            </Text>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>
              {state.settings.ownerName}
            </Text>
            <View style={styles.contactInfo}>
              <View style={styles.contactItem}>
                <Icon name="email" size={16} color={theme.colors.onSurfaceVariant} />
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 6 }} numberOfLines={1}>
                  {state.settings.email}
                </Text>
              </View>
              <View style={styles.contactItem}>
                <Icon name="phone" size={16} color={theme.colors.onSurfaceVariant} />
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 6 }}>
                  {state.settings.phone}
                </Text>
              </View>
              <View style={styles.contactItem}>
                <Icon name="map-marker" size={16} color={theme.colors.onSurfaceVariant} />
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 6 }} numberOfLines={2}>
                  {state.settings.address}
                </Text>
              </View>
            </View>
          </View>
        </View>
        <Button
          mode="contained"
          style={styles.editButton}
          onPress={() => editProfileBottomSheetRef.current?.expand()}
          icon="pencil"
          buttonColor={theme.colors.primary}
          textColor={theme.colors.onPrimary}
        >
          Edit Profile
        </Button>
      </View>
    </Surface>
  );

  const BusinessStatsCard = () => (
    <Surface style={[styles.statsCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <Text variant="titleLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
        Business Overview
      </Text>
      <View style={styles.statsGrid}>
        <View style={styles.statItem}>
          <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: 'bold' }}>
            {state.products.length}
          </Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
            Products
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text variant="headlineSmall" style={{ color: theme.colors.secondary, fontWeight: 'bold' }}>
            {state.orders.length}
          </Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
            Orders
          </Text>
        </View>
        <View style={styles.statItem}>
          <Text variant="headlineSmall" style={{ color: theme.colors.tertiary, fontWeight: 'bold' }}>
            {state.customers.length}
          </Text>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
            Customers
          </Text>
        </View>
      </View>
    </Surface>
  );

  const BusinessSection = () => (
    <Surface style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <Text variant="titleLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
        Business Settings
      </Text>

      <List.Item
        title="Store Information"
        description="Update store details and hours"
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.primary + '15' }]}>
            <Icon name="store" size={24} color={theme.colors.primary} />
          </View>
        )}
        right={() => <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
        onPress={() => setStoreHoursModalVisible(true)}
        style={styles.listItem}
      />

      <Divider style={{ marginVertical: 4 }} />

      <List.Item
        title="Payment Methods"
        description="Configure accepted payment options"
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.secondary + '15' }]}>
            <Icon name="cash-register" size={24} color={theme.colors.secondary} />
          </View>
        )}
        right={() => <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
        onPress={() => setPaymentMethodsModalVisible(true)}
        style={styles.listItem}
      />

      <Divider style={{ marginVertical: 4 }} />

      <List.Item
        title="Tax Settings"
        description={`Current rate: ${(state.settings.taxRate * 100).toFixed(1)}%`}
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.tertiary + '15' }]}>
            <Icon name="percent" size={24} color={theme.colors.tertiary} />
          </View>
        )}
        right={() => <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
        onPress={() => editProfileBottomSheetRef.current?.expand()}
        style={styles.listItem}
      />
    </Surface>
  );

  const ProfilePreferencesSection = () => (
    <Surface style={[styles.sectionCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
      <Text variant="titleLarge" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
        Profile Preferences
      </Text>

      <List.Item
        title="Notifications"
        description="Receive order and system notifications"
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.primary + '15' }]}>
            <Icon name="bell" size={24} color={theme.colors.primary} />
          </View>
        )}
        right={() => (
          <Switch
            value={notifications}
            onValueChange={(value) => handleSettingChange('notifications', value)}
            color={theme.colors.primary}
          />
        )}
        style={styles.listItem}
      />

      <Divider style={{ marginVertical: 4 }} />

      <List.Item
        title="Auto Backup"
        description="Automatically backup profile data daily"
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.secondary + '15' }]}>
            <Icon name="backup-restore" size={24} color={theme.colors.secondary} />
          </View>
        )}
        right={() => (
          <Switch
            value={autoBackup}
            onValueChange={(value) => handleSettingChange('autoBackup', value)}
            color={theme.colors.primary}
          />
        )}
        style={styles.listItem}
      />

      <Divider style={{ marginVertical: 4 }} />

      <List.Item
        title="Security"
        description="Manage passwords and security settings"
        titleStyle={{ fontWeight: '600', color: theme.colors.onSurface }}
        descriptionStyle={{ color: theme.colors.onSurfaceVariant }}
        left={(props) => (
          <View style={[styles.settingIcon, { backgroundColor: theme.colors.tertiary + '15' }]}>
            <Icon name="shield-check" size={24} color={theme.colors.tertiary} />
          </View>
        )}
        right={() => <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />}
        onPress={() => Alert.alert('Security', 'Security settings would be implemented here')}
        style={styles.listItem}
      />
    </Surface>
  );

  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="My Profile"
        subtitle="Personal & business settings"
        showSearch={false}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => navigation.goBack()}
      />
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <ProfileHeader />
        <BusinessStatsCard />
        <BusinessSection />
        <ProfilePreferencesSection />
      </ScrollView>

      {/* Bottom Sheets and Modals */}
      <EditProfileBottomSheet
        ref={editProfileBottomSheetRef}
        profile={state.settings}
        onSave={handleProfileSave}
        onClose={() => {}}
      />

      <StoreHoursModal
        visible={storeHoursModalVisible}
        onDismiss={() => setStoreHoursModalVisible(false)}
        onSave={handleStoreHoursSave}
        storeHours={state.settings.storeHours}
      />

      <PaymentMethodsModal
        visible={paymentMethodsModalVisible}
        onDismiss={() => setPaymentMethodsModalVisible(false)}
        onSave={handlePaymentMethodsSave}
        paymentMethods={state.settings.paymentMethods}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  profileCard: {
    marginBottom: 20,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  profileContent: {
    padding: 24,
  },
  profileHeader: {
    alignItems: 'center',
    marginBottom: 24,
  },
  avatarContainer: {
    padding: 12,
    borderRadius: 20,
    marginBottom: 16,
    position: 'relative',
  },
  statusIndicator: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    width: 16,
    height: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#FFFFFF',
  },
  profileInfo: {
    alignItems: 'center',
  },
  contactInfo: {
    marginTop: 12,
    alignItems: 'center',
  },
  contactItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 6,
  },
  editButton: {
    alignSelf: 'center',
  },
  statsCard: {
    marginBottom: 20,
    borderRadius: 16,
    padding: 20,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: 16,
  },
  statsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  sectionCard: {
    marginBottom: 20,
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  settingIcon: {
    width: 40,
    height: 40,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  listItem: {
    paddingVertical: 8,
  },

});

export default MyProfileScreen;
