import React, { useState, useMemo, useCallback } from 'react';
import { ScrollView, View, StyleSheet, TouchableOpacity, RefreshControl } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  Surface,
  Text,
  Button,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import { useFinancial } from '../context/FinancialContext';

import CommonHeader from '../components/CommonHeader';
import navigationService from '../services/NavigationService';
import UnifiedInfoCard from '../components/UnifiedInfoCard';
import { SPACING, BORDER_RADIUS, COMPONENT_SIZES, getBorderColor } from '../theme/designTokens';

const DashboardScreen = ({ navigation, navigateToTab }) => {
  const { theme } = useTheme();
  const { state } = useData();
  const { profitLossData, derivedData } = useFinancial();
  const insets = useSafeAreaInsets();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Memoize today's date to avoid recalculating
  const today = useMemo(() => new Date().toISOString().split('T')[0], []);

  // Simplified refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Simple refresh simulation
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('Dashboard refreshed');
    } catch (error) {
      console.log('Failed to refresh dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Simplified stats calculation
  const dashboardStats = useMemo(() => {
    const todaysSales = state.orders
      .filter(order => order.date === today && order.status === 'Completed')
      .reduce((sum, order) => sum + order.total, 0);

    return {
      todaysSales,
      totalOrders: state.orders.length,
      totalProducts: state.products.length,
      totalCustomers: state.customers.length,
    };
  }, [state.orders, state.products.length, state.customers.length, today]);

  const stats = useMemo(() => [
    { title: 'Today\'s Sales', value: `$${dashboardStats.todaysSales.toFixed(0)}`, icon: 'cash', color: theme.colors.primary, type: 'sales' },
    { title: 'Orders', value: dashboardStats.totalOrders.toString(), icon: 'clipboard-list', color: theme.colors.secondary, type: 'orders' },
    { title: 'Products', value: dashboardStats.totalProducts.toString(), icon: 'food-croissant', color: theme.colors.tertiary, type: 'products' },
    { title: 'Customers', value: dashboardStats.totalCustomers.toString(), icon: 'account-group', color: theme.colors.primary, type: 'customers' },
  ], [dashboardStats, theme.colors]);

  // Simplified recent orders
  const recentOrders = useMemo(() =>
    state.orders
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 2),
    [state.orders]
  );

  // Enhanced navigation handlers with proper data views
  const handleStatCardPress = useCallback((type) => {
    switch (type) {
      case 'sales':
        console.log('Fast navigation to Financial');
        navigateToTab('Financial');
        break;
      case 'orders':
        console.log('Fast navigation to Orders');
        navigateToTab('Orders');
        break;
      case 'products':
        console.log('Fast navigation to Products');
        try {
          // Navigate to Products screen using NavigationService
          navigationService.navigate('Products');
        } catch (error) {
          console.error('Failed to navigate to Products:', error);
          // Fallback to tab navigation
          navigateToTab('Products');
        }
        break;
      case 'customers':
        console.log('Fast navigation to Customers');
        // Navigate to customer management (MyProfile serves as customer view)
        try {
          navigationService.navigate('MyProfile');
        } catch (error) {
          console.error('Failed to navigate to customers:', error);
        }
        break;
    }
  }, [navigateToTab]);

  const handleOrderPress = useCallback((order) => {
    console.log('Order pressed:', order.id);
    navigateToTab('Orders');
  }, [navigateToTab]);







  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Dashboard"
        subtitle="Welcome back!"
        searchPlaceholder="Search orders, products, customers..."
        searchType="global"
        searchData={[...state.orders, ...state.products, ...state.customers]}
        searchFields={["name", "customerName", "customer", "title", "description"]}
        onSearchChange={setSearchQuery}
        onSearchResult={(item) => {
          console.log('Search result selected:', item);
          // Handle navigation based on item type
          if (item.customerName || item.customer) {
            // It's an order
            console.log('Navigate to order details');
          } else if (item.price) {
            // It's a product
            console.log('Navigate to product details');
          } else {
            // It's a customer
            console.log('Navigate to customer details');
          }
        }}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          console.log('Profile pressed - navigating to MyProfile');
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <ScrollView
        style={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        <View style={styles.content}>
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <UnifiedInfoCard
                key={index}
                type="stat"
                title={stat.title}
                value={stat.value}
                icon={stat.icon}
                iconColor={stat.color}
                onPress={() => handleStatCardPress(stat.type)}
                style={styles.statCardWrapper}
              />
            ))}
          </View>

          {/* Financial Analytics Summary Cards */}
          <View style={styles.financialSection}>
            <View style={styles.sectionHeader}>
              <Text variant="titleLarge" style={[styles.sectionTitle, { color: theme.colors.onBackground }]}>
                Financial Analytics
              </Text>
              <Button
                mode="text"
                onPress={() => navigateToTab('Financial')}
                textColor={theme.colors.primary}
                compact
              >
                View All
              </Button>
            </View>

            <View style={styles.financialCardsGrid}>
              {/* Profit & Loss Card */}
              <UnifiedInfoCard
                type="financial"
                title="Profit & Loss"
                subtitle={profitLossData ? `$${profitLossData.profit.net.toFixed(0)} net profit` : 'Generate report'}
                icon="chart-line"
                iconColor="#10B981"
                onPress={() => navigateToTab('Financial')}
                style={styles.financialCardWrapper}
                elevation={1}
              />

              {/* Expenses Card */}
              <UnifiedInfoCard
                type="financial"
                title="Expenses"
                subtitle={`$${derivedData?.totalExpenses?.toFixed(0) || '0'} total expenses`}
                icon="receipt"
                iconColor="#F59E0B"
                onPress={() => navigateToTab('Financial')}
                style={styles.financialCardWrapper}
                elevation={1}
              />
            </View>

            {/* Advanced Reports Card */}
            <TouchableOpacity
              style={styles.reportsCardWrapper}
              onPress={() => {
                console.log('Navigating to Advanced Reports...');
                try {
                  navigationService.navigate('Reports');
                } catch (error) {
                  console.error('Failed to navigate to Reports:', error);
                }
              }}
            >
              <Surface style={[styles.reportsCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
                <View style={styles.reportsCardContent}>
                  <View style={[styles.financialIconContainer, { backgroundColor: theme.colors.primary + '15' }]}>
                    <Icon name="file-chart" size={24} color={theme.colors.primary} />
                  </View>
                  <View style={styles.reportsCardText}>
                    <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                      Advanced Reports & Analytics
                    </Text>
                    <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
                      Sales trends, customer insights, product performance, and detailed analytics
                    </Text>
                  </View>
                  <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />
                </View>
              </Surface>
            </TouchableOpacity>
          </View>

        <View style={styles.ordersSection}>
          <View style={styles.sectionHeader}>
            <Text variant="titleLarge" style={[styles.sectionTitle, { color: theme.colors.onBackground }]}>
              Recent Orders
            </Text>
            <Button
              mode="text"
              onPress={() => navigateToTab('Orders')}
              textColor={theme.colors.primary}
              compact
            >
              View All
            </Button>
          </View>
          <View style={styles.ordersList}>
            {recentOrders.map((order, index) => (
              <UnifiedInfoCard
                key={order.id}
                type="order"
                data={order}
                status={order.status}
                statusColor={
                  order.status === 'Completed' ? theme.colors.tertiary :
                  order.status === 'In Progress' ? theme.colors.secondary :
                  order.status === 'Pending' ? theme.colors.primary :
                  theme.colors.error
                }
                description={order.items?.map(item => `${item.name} x${item.quantity}`).join(', ') || ''}
                onPress={() => handleOrderPress(order)}
              />
            ))}
          </View>
        </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: SPACING.lg,
  },
  statCardWrapper: {
    width: '48%',
    marginBottom: SPACING.sm,
  },
  statCard: {
    borderRadius: 10,
    padding: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  statContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statMain: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 28,
    height: 28,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  statText: {
    flex: 1,
    alignItems: 'flex-start',
  },
  chevron: {
    marginLeft: 4,
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.sm,
  },
  financialSection: {
    marginBottom: SPACING.lg,
  },
  financialCardsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  financialCardWrapper: {
    width: '48%',
  },
  financialIconContainer: {
    width: COMPONENT_SIZES.icon.xxxl,
    height: COMPONENT_SIZES.icon.xxxl,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm + 2,
  },
  reportsCardWrapper: {
    width: '100%',
  },
  reportsCard: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    borderWidth: 1,
  },
  reportsCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportsCardText: {
    flex: 1,
    marginLeft: 4,
  },
  ordersSection: {
    marginBottom: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  ordersList: {
    gap: SPACING.sm,
  },

});

export default DashboardScreen;
