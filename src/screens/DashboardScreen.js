import React, { useState, useRef, useMemo, useCallback, useEffect } from 'react';
import { ScrollView, View, StyleSheet, TouchableOpacity, RefreshControl } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  Card,
  Title,
  Paragraph,
  Surface,
  Text,
  Button,
  Divider,
  Searchbar,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import { useFinancial } from '../context/FinancialContext';
import ProductBottomSheet from '../components/ProductBottomSheet';
import OrderBottomSheet from '../components/OrderBottomSheet';
import OrderDetailsBottomSheet from '../components/OrderDetailsBottomSheet';
import StatsDetailsBottomSheet from '../components/StatsDetailsBottomSheet';
import CustomerDetailsBottomSheet from '../components/CustomerDetailsBottomSheet';
import SettingsBottomSheet from '../components/SettingsBottomSheet';

import CommonHeader from '../components/CommonHeader';
import UnifiedInfoCard from '../components/UnifiedInfoCard';
import navigationService from '../services/NavigationService';

const DashboardScreen = ({ navigation }) => {
  const { theme } = useTheme();
  const { state, actions } = useData();
  const { profitLossData, derivedData, generateProfitLossStatement } = useFinancial();
  const insets = useSafeAreaInsets();
  const productBottomSheetRef = useRef(null);
  const orderBottomSheetRef = useRef(null);
  const orderDetailsBottomSheetRef = useRef(null);
  const statsDetailsBottomSheetRef = useRef(null);
  const customerDetailsBottomSheetRef = useRef(null);
  const settingsBottomSheetRef = useRef(null);

  const [selectedOrder, setSelectedOrder] = useState(null);
  const [selectedStats, setSelectedStats] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [refreshing, setRefreshing] = useState(false);

  // Memoize today's date to avoid recalculating
  const today = useMemo(() => new Date().toISOString().split('T')[0], []);

  // Load financial data on component mount
  useEffect(() => {
    const loadFinancialData = async () => {
      try {
        const endDate = new Date().toISOString().split('T')[0];
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - 30); // Last 30 days
        await generateProfitLossStatement(startDate.toISOString().split('T')[0], endDate);
      } catch (error) {
        console.log('Failed to load financial data for dashboard:', error);
      }
    };

    loadFinancialData();
  }, [generateProfitLossStatement]);

  // Pull to refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Reload financial data
      const endDate = new Date().toISOString().split('T')[0];
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - 30);
      await generateProfitLossStatement(startDate.toISOString().split('T')[0], endDate);

      // You can add more data refresh logic here
      console.log('Dashboard data refreshed');
    } catch (error) {
      console.log('Failed to refresh dashboard data:', error);
    } finally {
      setRefreshing(false);
    }
  }, [generateProfitLossStatement]);

  // Memoize expensive calculations
  const dashboardStats = useMemo(() => {
    const completedOrders = state.orders.filter(order => order.status === 'Completed');
    const todaysOrders = state.orders.filter(order => order.date === today);
    const todaysSales = todaysOrders
      .filter(order => order.status === 'Completed')
      .reduce((sum, order) => sum + order.total, 0);

    const totalRevenue = completedOrders.reduce((sum, order) => sum + order.total, 0);

    return {
      todaysSales,
      totalOrders: state.orders.length,
      totalProducts: state.products.length,
      totalCustomers: state.customers.length,
      totalRevenue,
      completedOrders
    };
  }, [state.orders, state.products.length, state.customers.length, today]);

  const stats = useMemo(() => [
    { title: 'Today\'s Sales', value: `$${dashboardStats.todaysSales.toFixed(0)}`, icon: 'cash', color: theme.colors.primary, type: 'sales' },
    { title: 'Orders', value: dashboardStats.totalOrders.toString(), icon: 'clipboard-list', color: theme.colors.secondary, type: 'orders' },
    { title: 'Products', value: dashboardStats.totalProducts.toString(), icon: 'food-croissant', color: theme.colors.tertiary, type: 'products' },
    { title: 'Customers', value: dashboardStats.totalCustomers.toString(), icon: 'account-group', color: theme.colors.primary, type: 'customers' },
  ], [dashboardStats, theme.colors]);

  // Memoize recent orders
  const recentOrders = useMemo(() =>
    state.orders
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 3),
    [state.orders]
  );

  const StatCard = React.memo(({ title, value, icon, color, type }) => (
    <TouchableOpacity onPress={() => handleStatCardPress(type)} style={styles.statCardWrapper}>
      <Surface style={[styles.statCard, { backgroundColor: theme.colors.surface }]} elevation={0}>
        <View style={styles.statContent}>
          <View style={styles.statMain}>
            <View style={[styles.iconContainer, { backgroundColor: color + '15' }]}>
              <Icon name={icon} size={18} color={color} />
            </View>
            <View style={styles.statText}>
              <Text variant="headlineSmall" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                {value}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 1 }}>
                {title}
              </Text>
            </View>
          </View>
          <Icon name="chevron-right" size={14} color={theme.colors.onSurfaceVariant} style={styles.chevron} />
        </View>
      </Surface>
    </TouchableOpacity>
  ));

  const handleOrderPress = useCallback((order) => {
    setSelectedOrder(order);
    orderDetailsBottomSheetRef.current?.expand();
  }, []);

  const handleOrderStatusUpdate = useCallback((orderId, newStatus) => {
    actions.updateOrderStatus(orderId, newStatus);
  }, [actions]);

  const handleOrderEdit = useCallback((order) => {
    // Navigate to orders screen or open edit bottom sheet
    navigation.navigate('Orders');
  }, [navigation]);

  const handleOrderDelete = useCallback((order) => {
    actions.deleteOrder(order.id);
  }, [actions]);

  const handleStatCardPress = useCallback((statsType) => {
    let statsData = {};

    switch (statsType) {
      case 'sales':
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        const monthAgo = new Date();
        monthAgo.setMonth(monthAgo.getMonth() - 1);

        const weekSales = dashboardStats.completedOrders
          .filter(order => new Date(order.date) >= weekAgo)
          .reduce((sum, order) => sum + order.total, 0);

        const monthSales = dashboardStats.completedOrders
          .filter(order => new Date(order.date) >= monthAgo)
          .reduce((sum, order) => sum + order.total, 0);

        const avgOrderValue = dashboardStats.completedOrders.length > 0
          ? dashboardStats.totalRevenue / dashboardStats.completedOrders.length
          : 0;

        const ordersToday = state.orders.filter(order => order.date === today).length;

        const completionRate = dashboardStats.totalOrders > 0
          ? (dashboardStats.completedOrders.length / dashboardStats.totalOrders) * 100
          : 0;

        statsData = {
          todaysSales: dashboardStats.todaysSales,
          weekSales,
          monthSales,
          totalRevenue: dashboardStats.totalRevenue,
          avgOrderValue,
          ordersToday,
          completionRate
        };
        break;

      case 'orders':
        const ordersByStatus = state.orders.reduce((acc, order) => {
          acc[order.status] = (acc[order.status] || 0) + 1;
          return acc;
        }, {});

        const weekAgoForOrders = new Date();
        weekAgoForOrders.setDate(weekAgoForOrders.getDate() - 7);
        const ordersWeek = state.orders.filter(order => new Date(order.date) >= weekAgoForOrders).length;

        statsData = {
          pending: ordersByStatus['Pending'] || 0,
          inProgress: ordersByStatus['In Progress'] || 0,
          completed: ordersByStatus['Completed'] || 0,
          cancelled: ordersByStatus['Cancelled'] || 0,
          ordersToday: state.orders.filter(order => order.date === today).length,
          ordersWeek,
          avgProcessingTime: '15 min'
        };
        break;

      case 'products':
        const categories = {};
        state.products.forEach(product => {
          categories[product.category] = (categories[product.category] || 0) + 1;
        });

        const topProducts = state.products.map(product => ({
          name: product.name,
          sales: Math.floor(Math.random() * 50) // Mock sales data
        })).sort((a, b) => b.sales - a.sales);

        statsData = {
          categories: Object.entries(categories).map(([name, count]) => ({ name, count })),
          topProducts
        };
        break;

      case 'customers':
        const newCustomers = Math.floor(dashboardStats.totalCustomers * 0.3);
        const returningCustomers = dashboardStats.totalCustomers - newCustomers;
        const avgCustomerValue = dashboardStats.totalRevenue / Math.max(dashboardStats.totalCustomers, 1);
        const loyaltyRate = (returningCustomers / Math.max(dashboardStats.totalCustomers, 1)) * 100;

        statsData = {
          newCustomers,
          returningCustomers,
          avgCustomerValue,
          loyaltyRate
        };
        break;
    }

    setSelectedStats({
      type: statsType,
      data: statsData,
      title: statsType === 'sales' ? 'Sales Analytics' :
             statsType === 'orders' ? 'Order Management' :
             statsType === 'products' ? 'Product Insights' :
             'Customer Analytics',
      icon: statsType === 'sales' ? 'cash' :
            statsType === 'orders' ? 'clipboard-list' :
            statsType === 'products' ? 'food-croissant' :
            'account-group',
      color: statsType === 'sales' ? theme.colors.primary :
             statsType === 'orders' ? theme.colors.secondary :
             statsType === 'products' ? theme.colors.tertiary :
             theme.colors.primary
    });

    statsDetailsBottomSheetRef.current?.expand();
  }, [dashboardStats, state.orders, state.products, today, theme.colors, statsDetailsBottomSheetRef]);

  const handleCustomerPress = useCallback((customerName) => {
    // Find customer data (in real app, this would be from a proper customer database)
    const customerData = {
      name: customerName,
      phone: '+****************',
      email: customerName.toLowerCase().replace(' ', '.') + '@email.com',
      totalOrders: Math.floor(Math.random() * 20) + 1,
      totalSpent: Math.floor(Math.random() * 1000) + 100,
      createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
    };

    customerDetailsBottomSheetRef.current?.open(customerData);
  }, []);

  const OrderCard = React.memo(({ order }) => {
    const itemsText = useMemo(() =>
      order.items?.map(item => `${item.name} x${item.quantity}`).join(', ') || '',
      [order.items]
    );

    const statusColor = useMemo(() => {
      switch (order.status) {
        case 'Completed': return theme.colors.tertiary;
        case 'In Progress': return theme.colors.secondary;
        case 'Pending': return theme.colors.primary;
        default: return theme.colors.error;
      }
    }, [order.status, theme.colors]);

    return (
      <TouchableOpacity onPress={() => handleOrderPress(order)}>
        <Surface style={[styles.orderCard, { backgroundColor: theme.colors.surface }]} elevation={0}>
          <View style={styles.orderContent}>
            <View style={styles.orderHeader}>
              <View>
                <Text variant="titleMedium" style={{ fontWeight: '600' }}>#{order.id}</Text>
                <TouchableOpacity onPress={() => handleCustomerPress(order.customer)}>
                  <Text variant="bodySmall" style={{ color: theme.colors.primary, marginTop: 2, textDecorationLine: 'underline' }}>
                    {order.customer}
                  </Text>
                </TouchableOpacity>
              </View>
              <View style={styles.orderMeta}>
                <Text variant="titleSmall" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                  ${order.total.toFixed(2)}
                </Text>
                <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
              </View>
            </View>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 8 }}>
              {itemsText}
            </Text>
            <View style={styles.orderFooter}>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                {order.time} • {order.status}
              </Text>
              <Icon name="chevron-right" size={16} color={theme.colors.onSurfaceVariant} />
            </View>
          </View>
        </Surface>
      </TouchableOpacity>
    );
  });



  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Dashboard"
        subtitle="Welcome back!"
        searchPlaceholder="Search orders, products, customers..."
        searchType="global"
        searchData={[...state.orders, ...state.products, ...state.customers]}
        searchFields={["name", "customerName", "customer", "title", "description"]}
        onSearchChange={setSearchQuery}
        onSearchResult={(item) => {
          console.log('Search result selected:', item);
          // Handle navigation based on item type
          if (item.customerName || item.customer) {
            // It's an order
            console.log('Navigate to order details');
          } else if (item.price) {
            // It's a product
            console.log('Navigate to product details');
          } else {
            // It's a customer
            console.log('Navigate to customer details');
          }
        }}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          // Navigate to MyProfile using the root navigation
          const rootNavigation = navigation.getParent('AppNavigator') || navigation.getParent();
          if (rootNavigation) {
            rootNavigation.navigate('MyProfile');
          } else {
            console.log('Could not find root navigation');
          }
        }}
      />

      <ScrollView
        style={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        <View style={styles.content}>
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <UnifiedInfoCard
                key={index}
                type="stat"
                title={stat.title}
                value={stat.value}
                icon={stat.icon}
                iconColor={stat.color}
                onPress={() => handleStatCardPress(stat.type)}
                style={styles.statCardWrapper}
              />
            ))}
          </View>

          {/* Financial Analytics Summary Cards */}
          <View style={styles.financialSection}>
            <View style={styles.sectionHeader}>
              <Text variant="titleLarge" style={[styles.sectionTitle, { color: theme.colors.onBackground }]}>
                Financial Analytics
              </Text>
              <Button
                mode="text"
                onPress={() => navigation.navigate('Financial')}
                textColor={theme.colors.primary}
                compact
              >
                View All
              </Button>
            </View>

            <View style={styles.financialCardsGrid}>
              {/* Profit & Loss Card */}
              <UnifiedInfoCard
                type="financial"
                title="Profit & Loss"
                subtitle={profitLossData ? `$${profitLossData.profit.net.toFixed(0)} net profit` : 'Generate report'}
                icon="chart-line"
                iconColor="#10B981"
                onPress={() => navigation.navigate('Financial')}
                style={styles.financialCardWrapper}
                elevation={1}
              />

              {/* Expenses Card */}
              <UnifiedInfoCard
                type="financial"
                title="Expenses"
                subtitle={`$${derivedData?.totalExpenses?.toFixed(0) || '0'} total expenses`}
                icon="receipt"
                iconColor="#F59E0B"
                onPress={() => navigation.navigate('Financial')}
                style={styles.financialCardWrapper}
                elevation={1}
              />
            </View>

            {/* Advanced Reports Card */}
            <TouchableOpacity
              style={styles.reportsCardWrapper}
              onPress={() => {
                const rootNavigation = navigation.getParent('AppNavigator') || navigation.getParent();
                if (rootNavigation) {
                  rootNavigation.navigate('Reports');
                }
              }}
            >
              <Surface style={[styles.reportsCard, { backgroundColor: theme.colors.surface }]} elevation={1}>
                <View style={styles.reportsCardContent}>
                  <View style={[styles.financialIconContainer, { backgroundColor: theme.colors.primary + '15' }]}>
                    <Icon name="file-chart" size={24} color={theme.colors.primary} />
                  </View>
                  <View style={styles.reportsCardText}>
                    <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                      Advanced Reports & Analytics
                    </Text>
                    <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
                      Sales trends, customer insights, product performance, and detailed analytics
                    </Text>
                  </View>
                  <Icon name="chevron-right" size={20} color={theme.colors.onSurfaceVariant} />
                </View>
              </Surface>
            </TouchableOpacity>
          </View>

        <View style={styles.ordersSection}>
          <View style={styles.sectionHeader}>
            <Text variant="titleLarge" style={[styles.sectionTitle, { color: theme.colors.onBackground }]}>
              Recent Orders
            </Text>
            <Button
              mode="text"
              onPress={() => navigation.navigate('Orders')}
              textColor={theme.colors.primary}
              compact
            >
              View All
            </Button>
          </View>
          <View style={styles.ordersList}>
            {recentOrders.map((order, index) => (
              <UnifiedInfoCard
                key={order.id}
                type="order"
                data={order}
                status={order.status}
                statusColor={
                  order.status === 'Completed' ? theme.colors.tertiary :
                  order.status === 'In Progress' ? theme.colors.secondary :
                  order.status === 'Pending' ? theme.colors.primary :
                  theme.colors.error
                }
                description={order.items?.map(item => `${item.name} x${item.quantity}`).join(', ') || ''}
                onPress={() => handleOrderPress(order)}
              />
            ))}
          </View>
        </View>
        </View>
      </ScrollView>

      <ProductBottomSheet
        bottomSheetRef={productBottomSheetRef}
        onSave={(product) => actions.addProduct(product)}
        mode="add"
      />

      <OrderBottomSheet
        bottomSheetRef={orderBottomSheetRef}
        onSave={(order) => actions.addOrder(order)}
        mode="add"
      />

      <OrderDetailsBottomSheet
        bottomSheetRef={orderDetailsBottomSheetRef}
        order={selectedOrder}
        onUpdateStatus={handleOrderStatusUpdate}
        onEdit={handleOrderEdit}
        onDelete={handleOrderDelete}
      />

      <StatsDetailsBottomSheet
        bottomSheetRef={statsDetailsBottomSheetRef}
        statsType={selectedStats?.type}
        data={selectedStats?.data}
        title={selectedStats?.title}
        icon={selectedStats?.icon}
        color={selectedStats?.color}
      />

      <CustomerDetailsBottomSheet
        ref={customerDetailsBottomSheetRef}
      />

      <SettingsBottomSheet
        ref={settingsBottomSheetRef}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: 16,
  },
  searchBar: {
    borderRadius: 25,
    height: 48,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statCardWrapper: {
    width: '48%',
    marginBottom: 8,
  },
  statCard: {
    borderRadius: 10,
    padding: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  statContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statMain: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 28,
    height: 28,
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 8,
  },
  statText: {
    flex: 1,
    alignItems: 'flex-start',
  },
  chevron: {
    marginLeft: 4,
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: 8,
  },
  financialSection: {
    marginBottom: 16,
  },
  financialCardsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  financialCardWrapper: {
    width: '48%',
  },
  financialCard: {
    borderRadius: 12,
    padding: 12,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  financialCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  financialIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  financialCardText: {
    flex: 1,
  },
  reportsCardWrapper: {
    width: '100%',
  },
  reportsCard: {
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  reportsCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportsCardText: {
    flex: 1,
    marginLeft: 4,
  },
  ordersSection: {
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  ordersList: {
    gap: 6,
  },
  orderCard: {
    borderRadius: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.05)',
  },
  orderContent: {
    padding: 10,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  orderMeta: {
    alignItems: 'flex-end',
  },
  orderFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 6,
  },
  statusDot: {
    width: 5,
    height: 5,
    borderRadius: 2.5,
    marginTop: 3,
  },
});

export default DashboardScreen;
