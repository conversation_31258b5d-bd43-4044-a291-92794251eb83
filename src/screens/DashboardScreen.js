import React, { useState, useMemo, useCallback } from 'react';
import { ScrollView, View, StyleSheet, TouchableOpacity, RefreshControl } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import {
  Surface,
  Text,
  Button,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import { useTheme } from '../context/ThemeContext';
import { useFinancial } from '../context/FinancialContext';

import CommonHeader from '../components/CommonHeader';
import navigationService from '../services/NavigationService';
import UnifiedInfoCard from '../components/UnifiedInfoCard';
import { SPACING, BORDER_RADIUS, COMPONENT_SIZES, TYPOGRAPHY, getBorderColor } from '../theme/designTokens';

const DashboardScreen = ({ navigation, navigateToTab }) => {
  const { theme } = useTheme();
  const { state } = useData();
  const { profitLossData, derivedData } = useFinancial();
  const insets = useSafeAreaInsets();

  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Memoize today's date to avoid recalculating
  const today = useMemo(() => new Date().toISOString().split('T')[0], []);

  // Simplified refresh functionality
  const handleRefresh = useCallback(async () => {
    setRefreshing(true);
    try {
      // Simple refresh simulation
      await new Promise(resolve => setTimeout(resolve, 500));
      console.log('Dashboard refreshed');
    } catch (error) {
      console.log('Failed to refresh dashboard:', error);
    } finally {
      setRefreshing(false);
    }
  }, []);

  // Simplified stats calculation
  const dashboardStats = useMemo(() => {
    const todaysSales = state.orders
      .filter(order => order.date === today && order.status === 'Completed')
      .reduce((sum, order) => sum + order.total, 0);

    return {
      todaysSales,
      totalOrders: state.orders.length,
      totalProducts: state.products.length,
      totalCustomers: state.customers.length,
    };
  }, [state.orders, state.products.length, state.customers.length, today]);

  const stats = useMemo(() => [
    { title: `৳${dashboardStats.todaysSales.toFixed(0)}`, value: 'Today\'s Sales', icon: 'cash', color: theme.colors.primary, type: 'sales' },
    { title: dashboardStats.totalOrders.toString(), value: 'Total Orders', icon: 'clipboard-list', color: theme.colors.secondary, type: 'orders' },
    { title: dashboardStats.totalProducts.toString(), value: 'Total Products', icon: 'food-croissant', color: theme.colors.tertiary, type: 'products' },
    { title: dashboardStats.totalCustomers.toString(), value: 'Total Customers', icon: 'account-group', color: theme.colors.primary, type: 'customers' },
    { title: profitLossData ? `৳${profitLossData.profit.net.toFixed(0)}` : '৳0', value: 'Net Profit', icon: 'chart-line', color: profitLossData?.profit.net >= 0 ? "#10B981" : "#EF4444", type: 'financial' },
    { title: `৳${derivedData?.totalExpenses?.toFixed(0) || '0'}`, value: 'Total Expenses', icon: 'receipt', color: "#F59E0B", type: 'financial' },
  ], [dashboardStats, theme.colors, profitLossData, derivedData]);

  const reportsCard = useMemo(() => ({
    title: 'View Reports',
    value: 'Advanced Analytics',
    icon: 'file-chart',
    color: "#8B5CF6",
    type: 'reports'
  }), []);

  // Simplified recent orders
  const recentOrders = useMemo(() =>
    state.orders
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      .slice(0, 2),
    [state.orders]
  );

  // Enhanced navigation handlers with proper data views
  const handleStatCardPress = useCallback((type) => {
    switch (type) {
      case 'sales':
        console.log('Fast navigation to Sales Reports');
        try {
          // Navigate to Reports screen
          navigationService.navigate('Reports');
        } catch (error) {
          console.error('Failed to navigate to Reports:', error);
        }
        break;
      case 'orders':
        console.log('Fast navigation to Orders');
        navigateToTab('Orders');
        break;
      case 'products':
        console.log('Fast navigation to Products');
        navigateToTab('Products');
        break;
      case 'customers':
        console.log('Fast navigation to Customers');
        try {
          // Navigate to dedicated Customers screen
          navigationService.navigate('Customers');
        } catch (error) {
          console.error('Failed to navigate to Customers:', error);
        }
        break;
      case 'financial':
        console.log('Fast navigation to Financial');
        navigateToTab('Financial');
        break;
      case 'reports':
        console.log('Navigating to Advanced Reports...');
        try {
          navigationService.navigate('Reports');
        } catch (error) {
          console.error('Failed to navigate to Reports:', error);
        }
        break;
    }
  }, [navigateToTab]);

  const handleOrderPress = useCallback((order) => {
    console.log('Order pressed:', order.id);
    navigateToTab('Orders');
  }, [navigateToTab]);







  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background }]}>
      <CommonHeader
        title="Dashboard"
        subtitle="Welcome back!"
        searchPlaceholder="Search orders, products, customers..."
        searchType="global"
        searchData={[...state.orders, ...state.products, ...state.customers]}
        searchFields={["name", "customerName", "customer", "title", "description"]}
        onSearchChange={setSearchQuery}
        onSearchResult={(item) => {
          console.log('Search result selected:', item);
          // Handle navigation based on item type
          if (item.customerName || item.customer) {
            // It's an order
            console.log('Navigate to order details');
          } else if (item.price) {
            // It's a product
            console.log('Navigate to product details');
          } else {
            // It's a customer
            console.log('Navigate to customer details');
          }
        }}
        onNotificationPress={() => console.log('Notifications pressed')}
        onProfilePress={() => {
          console.log('Profile pressed - navigating to MyProfile');
          try {
            navigationService.navigate('MyProfile');
          } catch (error) {
            console.error('Failed to navigate to MyProfile:', error);
          }
        }}
      />

      <ScrollView
        style={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        <View style={styles.content}>
          <View style={styles.statsGrid}>
            {stats.map((stat, index) => (
              <UnifiedInfoCard
                key={index}
                type="stat"
                title={stat.title}
                value={stat.value}
                icon={stat.icon}
                iconColor={stat.color}
                onPress={() => handleStatCardPress(stat.type)}
                style={styles.statCardWrapper}
              />
            ))}
          </View>

          {/* Full Width Reports Card */}
          <UnifiedInfoCard
            type="stat"
            title={reportsCard.title}
            value={reportsCard.value}
            icon={reportsCard.icon}
            iconColor={reportsCard.color}
            onPress={() => handleStatCardPress(reportsCard.type)}
            style={styles.fullWidthCard}
          />



        <View style={styles.ordersSection}>
          <View style={styles.sectionHeader}>
            <Text style={[styles.sectionTitle, {
              color: theme.colors.onBackground,
              fontSize: TYPOGRAPHY.sectionTitle.fontSize,
              fontWeight: TYPOGRAPHY.sectionTitle.fontWeight,
              lineHeight: TYPOGRAPHY.sectionTitle.lineHeight * TYPOGRAPHY.sectionTitle.fontSize
            }]}>
              Recent Orders
            </Text>
            <Button
              mode="text"
              onPress={() => navigateToTab('Orders')}
              textColor={theme.colors.primary}
              compact
            >
              View All
            </Button>
          </View>
          <View style={styles.ordersList}>
            {recentOrders.map((order, index) => (
              <UnifiedInfoCard
                key={order.id}
                type="order"
                data={order}
                status={order.status}
                statusColor={
                  order.status === 'Completed' ? theme.colors.tertiary :
                  order.status === 'In Progress' ? theme.colors.secondary :
                  order.status === 'Pending' ? theme.colors.primary :
                  theme.colors.error
                }
                description={order.items?.map(item => `${item.name} x${item.quantity}`).join(', ') || ''}
                onPress={() => handleOrderPress(order)}
              />
            ))}
          </View>
        </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flex: 1,
  },
  content: {
    padding: SPACING.lg,
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
    gap: SPACING.xs,
  },
  statCardWrapper: {
    width: '48%',
    marginBottom: SPACING.xs,
  },
  fullWidthCard: {
    width: '100%',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontWeight: '700',
    marginBottom: SPACING.sm,
  },
  financialSection: {
    marginBottom: SPACING.lg,
  },
  financialCardsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: SPACING.md,
  },
  financialCardWrapper: {
    width: '48%',
  },
  financialIconContainer: {
    width: COMPONENT_SIZES.icon.xxxl,
    height: COMPONENT_SIZES.icon.xxxl,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.sm + 2,
  },
  reportsCardWrapper: {
    width: '100%',
  },
  reportsCard: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    borderWidth: 1,
  },
  reportsCardContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  reportsCardText: {
    flex: 1,
    marginLeft: 4,
  },
  ordersSection: {
    marginBottom: SPACING.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  ordersList: {
    gap: SPACING.sm,
  },

});

export default DashboardScreen;
