import 'react-native-gesture-handler/jestSetup';

// Mock react-native-paper
jest.mock('react-native-paper', () => {
  const RealModule = jest.requireActual('react-native-paper');
  const MockedModule = {
    ...RealModule,
    Portal: ({ children }) => children,
  };
  return MockedModule;
});

// Mock expo modules
jest.mock('expo-sqlite', () => ({
  openDatabase: jest.fn(() => ({
    transaction: jest.fn(),
    executeSql: jest.fn(),
  })),
}));

jest.mock('expo-image-picker', () => ({
  launchImageLibraryAsync: jest.fn(),
  launchCameraAsync: jest.fn(),
  requestCameraPermissionsAsync: jest.fn(),
  requestMediaLibraryPermissionsAsync: jest.fn(),
  MediaType: {
    Images: 'Images',
    Videos: 'Videos',
    All: 'All',
  },
  ImagePickerResult: {},
}));

jest.mock('expo-barcode-scanner', () => ({
  BarCodeScanner: {
    requestPermissionsAsync: jest.fn(),
    Constants: {
      BarCodeType: {
        qr: 'qr',
      },
    },
  },
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  getAllKeys: jest.fn(),
  multiGet: jest.fn(),
  multiSet: jest.fn(),
}));

// Mock react-native-vector-icons
jest.mock('react-native-vector-icons/MaterialIcons', () => 'MaterialIcons');

// Mock navigation
jest.mock('@react-navigation/native', () => ({
  useNavigation: () => ({
    navigate: jest.fn(),
    goBack: jest.fn(),
    dispatch: jest.fn(),
  }),
  useRoute: () => ({
    params: {},
  }),
  useFocusEffect: jest.fn(),
}));

// Global test utilities
global.console = {
  ...console,
  // Suppress console.log in tests unless needed
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock performance.now for testing
global.performance = {
  now: jest.fn(() => Date.now()),
};

// Setup test data
global.testData = {
  product: {
    id: 'test-product-1',
    name: 'Test Cake',
    description: 'A delicious test cake',
    price: 25.99,
    category: 'Cakes',
    stock: 10,
    sku: 'CAKE-001',
    barcode: '1234567890',
    image: null,
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  customer: {
    id: 'test-customer-1',
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    address: '123 Test Street',
    totalOrders: 5,
    totalSpent: 125.50,
    lastOrderDate: new Date().toISOString(),
    isActive: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
  order: {
    id: 'test-order-1',
    customerName: 'John Doe',
    customer: 'John Doe',
    email: '<EMAIL>',
    phone: '+1234567890',
    date: new Date().toISOString().split('T')[0],
    time: '10:30:00',
    status: 'pending',
    orderType: 'pickup',
    subtotal: 25.99,
    tax: 1.30,
    discount: 0,
    total: 27.29,
    notes: 'Test order',
    image: null,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  },
};
