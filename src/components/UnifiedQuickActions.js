/**
 * UnifiedQuickActions - Centralized quick actions component
 * Integrates with unified navigation system for seamless actions
 */

import React from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, Card, IconButton, useTheme } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import useUnifiedNavigation from '../hooks/useUnifiedNavigation';
import CustomBottomSheet from './CustomBottomSheet';

const UnifiedQuickActions = ({ isVisible, onClose }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const {
    executeQuickAction,
    navigateToTab,
    openSettings,
    goToProducts,
  } = useUnifiedNavigation();

  const quickActions = [
    {
      id: 'add-product',
      title: 'Add Product',
      subtitle: 'Create new product',
      icon: 'package-variant',
      color: theme.colors.primary,
      action: () => executeQuickAction('add-product'),
    },
    {
      id: 'add-order',
      title: 'New Order',
      subtitle: 'Create customer order',
      icon: 'clipboard-plus',
      color: theme.colors.secondary,
      action: () => executeQuickAction('add-order'),
    },
    {
      id: 'scan',
      title: 'Scan Code',
      subtitle: 'QR & Barcode scanner',
      icon: 'qrcode-scan',
      color: theme.colors.tertiary,
      action: () => executeQuickAction('scan'),
    },
    {
      id: 'financial',
      title: 'Financial',
      subtitle: 'View financial data',
      icon: 'chart-line',
      color: theme.colors.primary,
      action: () => executeQuickAction('financial'),
    },
    {
      id: 'reports',
      title: 'Reports',
      subtitle: 'Analytics & insights',
      icon: 'chart-bar',
      color: theme.colors.secondary,
      action: () => executeQuickAction('reports'),
    },
    {
      id: 'profile',
      title: 'Profile',
      subtitle: 'Business settings',
      icon: 'account-cog',
      color: theme.colors.tertiary,
      action: () => executeQuickAction('profile'),
    },
  ];

  const handleActionPress = (action) => {
    action.action();
    onClose?.();
  };

  return (
    <CustomBottomSheet
      isVisible={isVisible}
      onClose={onClose}
      title="Quick Actions"
      subtitle="Choose an action to perform"
    >
      <ScrollView
        style={styles.container}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
      >
        <View style={styles.actionsGrid}>
          {quickActions.map((action) => (
            <Card
              key={action.id}
              style={[
                styles.actionCard,
                { backgroundColor: theme.colors.surface }
              ]}
              onPress={() => handleActionPress(action)}
              mode="contained"
            >
              <Card.Content style={styles.cardContent}>
                <View style={styles.iconContainer}>
                  <View
                    style={[
                      styles.iconBackground,
                      { backgroundColor: action.color + '20' }
                    ]}
                  >
                    <Icon
                      name={action.icon}
                      size={28}
                      color={action.color}
                    />
                  </View>
                </View>

                <View style={styles.textContainer}>
                  <Text
                    variant="titleMedium"
                    style={[
                      styles.actionTitle,
                      { color: theme.colors.onSurface }
                    ]}
                  >
                    {action.title}
                  </Text>
                  <Text
                    variant="bodySmall"
                    style={[
                      styles.actionSubtitle,
                      { color: theme.colors.onSurfaceVariant }
                    ]}
                  >
                    {action.subtitle}
                  </Text>
                </View>

                <IconButton
                  icon="chevron-right"
                  size={20}
                  iconColor={theme.colors.onSurfaceVariant}
                  style={styles.chevronIcon}
                />
              </Card.Content>
            </Card>
          ))}
        </View>

        {/* Quick Navigation Section */}
        <View style={styles.section}>
          <Text
            variant="titleSmall"
            style={[
              styles.sectionTitle,
              { color: theme.colors.onSurface }
            ]}
          >
            Quick Navigation
          </Text>

          <View style={styles.navigationRow}>
            {[
              { name: 'Dashboard', icon: 'view-dashboard', route: 'Dashboard' },
              { name: 'Orders', icon: 'clipboard-list', route: 'Orders' },
              { name: 'Products', icon: 'package-variant', action: () => goToProducts() },
              { name: 'Settings', icon: 'cog', action: () => openSettings() },
            ].map((nav) => (
              <Card
                key={nav.name}
                style={[
                  styles.navCard,
                  { backgroundColor: theme.colors.surfaceVariant }
                ]}
                onPress={() => {
                  if (nav.route) {
                    navigateToTab(nav.route);
                  } else if (nav.action) {
                    nav.action();
                  }
                  onClose?.();
                }}
              >
                <Card.Content style={styles.navCardContent}>
                  <Icon
                    name={nav.icon}
                    size={24}
                    color={theme.colors.primary}
                  />
                  <Text
                    variant="labelSmall"
                    style={[
                      styles.navLabel,
                      { color: theme.colors.onSurfaceVariant }
                    ]}
                  >
                    {nav.name}
                  </Text>
                </Card.Content>
              </Card>
            ))}
          </View>
        </View>
      </ScrollView>
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 20,
  },
  actionsGrid: {
    gap: 12,
  },
  actionCard: {
    marginHorizontal: 0,
    elevation: 2,
  },
  cardContent: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 16,
  },
  iconContainer: {
    marginRight: 16,
  },
  iconBackground: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  textContainer: {
    flex: 1,
  },
  actionTitle: {
    fontWeight: '600',
    marginBottom: 2,
  },
  actionSubtitle: {
    opacity: 0.8,
  },
  chevronIcon: {
    margin: 0,
  },
  section: {
    marginTop: 24,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 12,
    paddingHorizontal: 4,
  },
  navigationRow: {
    flexDirection: 'row',
    gap: 8,
  },
  navCard: {
    flex: 1,
    elevation: 1,
  },
  navCardContent: {
    alignItems: 'center',
    paddingVertical: 16,
    paddingHorizontal: 8,
  },
  navLabel: {
    marginTop: 8,
    textAlign: 'center',
    fontWeight: '500',
  },
});

export default UnifiedQuickActions;
