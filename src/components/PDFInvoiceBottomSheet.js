import React, { forwardRef, useState, useCallback } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import { Text, Card, Button, Divider, ActivityIndicator } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CustomBottomSheet from './CustomBottomSheet';
import { useTheme } from '../context/ThemeContext';
import { PDFInvoiceGenerator } from '../utils/pdfInvoiceGenerator';

const PDFInvoiceBottomSheet = forwardRef(({ order, onClose }, ref) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const [isPrinting, setIsPrinting] = useState(false);
  const [htmlContent, setHtmlContent] = useState('');

  React.useEffect(() => {
    if (order) {
      const html = PDFInvoiceGenerator.generateInvoiceHTML(order);
      setHtmlContent(html);
    }
  }, [order]);

  const handlePrint = useCallback(async () => {
    if (!order) return;

    setIsPrinting(true);
    try {
      await PDFInvoiceGenerator.printInvoice(order);
      Alert.alert('Success', 'Invoice sent to printer successfully!');
    } catch (error) {
      console.error('Print error:', error);
      Alert.alert('Print Error', 'Failed to print invoice. Please try again.');
    } finally {
      setIsPrinting(false);
    }
  }, [order]);

  const handleGeneratePDF = useCallback(async () => {
    if (!order) return;

    try {
      const pdf = await PDFInvoiceGenerator.generatePDF(order);
      Alert.alert(
        'PDF Generated',
        `Invoice PDF saved successfully!\nLocation: ${pdf.filePath}`,
        [
          { text: 'OK' },
          {
            text: 'Share',
            onPress: async () => {
              try {
                await PDFInvoiceGenerator.generateAndSharePDF(order);
              } catch (error) {
                Alert.alert('Share Error', 'Failed to share PDF invoice.');
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error('PDF generation error:', error);
      Alert.alert('PDF Error', 'Failed to generate PDF invoice. Please try again.');
    }
  }, [order]);

  if (!order) return null;

  const subtotal = order.total;
  const tax = subtotal * 0.08;
  const total = subtotal + tax;

  return (
    <CustomBottomSheet
      ref={ref}
      snapPoints={['95%']}
      onClose={onClose}
    >
      <View style={styles.header}>
        <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
          Invoice #{order.id}
        </Text>
        <Button
          mode="text"
          onPress={onClose}
          icon="close"
          textColor={theme.colors.onSurfaceVariant}
        >
          Close
        </Button>
      </View>

      {/* Invoice Preview */}
      <ScrollView style={styles.previewContainer} showsVerticalScrollIndicator={false}>
        <Card style={styles.invoiceCard}>
          <Card.Content>
            <Text variant="headlineSmall" style={[styles.invoiceTitle, { color: theme.colors.onSurface }]}>
              SWEET DELIGHTS BAKERY
            </Text>
            <Text variant="bodyMedium" style={[styles.invoiceSubtitle, { color: theme.colors.onSurfaceVariant }]}>
              Invoice #{order?.id}
            </Text>

            <Divider style={styles.divider} />

            <View style={styles.invoiceDetails}>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginBottom: 8 }}>
                Customer: {order?.customer}
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginBottom: 4 }}>
                Date: {new Date(order?.createdAt).toLocaleDateString()}
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginBottom: 16 }}>
                Status: {order?.status}
              </Text>

              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginBottom: 8 }}>
                Order Items:
              </Text>
              {order?.items?.map((item, index) => (
                <View key={index} style={styles.itemRow}>
                  <Text style={{ color: theme.colors.onSurface, flex: 1 }}>
                    {item.name} x{item.quantity}
                  </Text>
                  <Text style={{ color: theme.colors.onSurface }}>
                    ${(item.price * item.quantity).toFixed(2)}
                  </Text>
                </View>
              ))}

              <Divider style={styles.divider} />

              <View style={styles.totalRow}>
                <Text variant="titleLarge" style={{ color: theme.colors.onSurface }}>
                  Total: ${order?.total?.toFixed(2)}
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>
      </ScrollView>

      {/* Action Buttons */}
      <View style={[styles.actions, { paddingBottom: insets.bottom }]}>
        <View style={styles.actionRow}>
          <Button
            mode="outlined"
            onPress={handleGeneratePDF}
            icon="download"
            style={styles.actionButton}
          >
            Download PDF
          </Button>
          <Button
            mode="contained"
            onPress={handlePrint}
            icon="printer"
            style={styles.actionButton}
            disabled={isPrinting}
            loading={isPrinting}
          >
            {isPrinting ? 'Printing...' : 'Print'}
          </Button>
        </View>
      </View>
    </CustomBottomSheet>
  );
});

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  title: {
    fontWeight: '700',
  },
  previewContainer: {
    flex: 1,
    padding: 16,
  },
  invoiceCard: {
    borderRadius: 12,
  },
  invoiceTitle: {
    textAlign: 'center',
    fontWeight: '700',
    marginBottom: 8,
  },
  invoiceSubtitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  divider: {
    marginVertical: 16,
  },
  invoiceDetails: {
    paddingVertical: 8,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  totalRow: {
    alignItems: 'flex-end',
    paddingTop: 8,
  },
  actions: {
    paddingHorizontal: 14,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  actionRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 6,
  },
});

export default PDFInvoiceBottomSheet;
