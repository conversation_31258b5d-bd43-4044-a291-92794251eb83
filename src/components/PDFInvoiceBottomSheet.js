/**
 * PDFInvoiceBottomSheet - Unified Invoice Preview and Generation
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import React, { forwardRef, useState, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Alert, Keyboard } from 'react-native';
import {
  Text,
  Card,
  Button,
  Divider,
  ActivityIndicator,
  IconButton,
  Portal,
  useTheme as usePaperTheme
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useTheme } from '../context/ThemeContext';
import { PDFInvoiceGenerator } from '../utils/pdfInvoiceGenerator';

const PDFInvoiceBottomSheet = forwardRef(({ order, onClose }, ref) => {
  const { theme } = useTheme();
  const paperTheme = usePaperTheme();
  const insets = useSafeAreaInsets();
  const [isPrinting, setIsPrinting] = useState(false);
  const [htmlContent, setHtmlContent] = useState('');

  const snapPoints = useMemo(() => ['95%'], []);

  React.useEffect(() => {
    if (order) {
      const html = PDFInvoiceGenerator.generateInvoiceHTML(order);
      setHtmlContent(html);
    }
  }, [order]);

  const handlePrint = useCallback(async () => {
    if (!order) return;

    setIsPrinting(true);
    try {
      await PDFInvoiceGenerator.printInvoice(order);
      Alert.alert('Success', 'Invoice sent to printer successfully!');
    } catch (error) {
      console.error('Print error:', error);
      Alert.alert('Print Error', 'Failed to print invoice. Please try again.');
    } finally {
      setIsPrinting(false);
    }
  }, [order]);

  const handleGeneratePDF = useCallback(async () => {
    if (!order) return;

    try {
      const pdf = await PDFInvoiceGenerator.generatePDF(order);
      Alert.alert(
        'PDF Generated',
        `Invoice PDF saved successfully!\nLocation: ${pdf.filePath}`,
        [
          { text: 'OK' },
          {
            text: 'Share',
            onPress: async () => {
              try {
                await PDFInvoiceGenerator.generateAndSharePDF(order);
              } catch (error) {
                Alert.alert('Share Error', 'Failed to share PDF invoice.');
              }
            },
          },
        ]
      );
    } catch (error) {
      console.error('PDF generation error:', error);
      Alert.alert('PDF Error', 'Failed to generate PDF invoice. Please try again.');
    }
  }, [order]);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
      if (onClose) onClose();
    }
  }, [onClose]);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  if (!order) return null;

  const subtotal = order.total;
  const tax = subtotal * 0.08;
  const total = subtotal + tax;

  return (
    <Portal>
      <BottomSheet
        ref={ref}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: paperTheme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: paperTheme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: paperTheme.colors.outline + '20' }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: paperTheme.colors.tertiary + '15' }]}>
                <Icon name="file-document" size={24} color={paperTheme.colors.tertiary} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: paperTheme.colors.onSurface }]}>
                  Invoice #{order.id}
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: paperTheme.colors.onSurfaceVariant }]}>
                  {order.customer} • {new Date(order.createdAt).toLocaleDateString()}
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => ref.current?.close()}
              iconColor={paperTheme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            <Card style={styles.invoiceCard}>
              <Card.Content>
                <Text variant="headlineSmall" style={[styles.invoiceTitle, { color: paperTheme.colors.onSurface }]}>
                  SWEET DELIGHTS BAKERY
                </Text>
                <Text variant="bodyMedium" style={[styles.invoiceSubtitle, { color: paperTheme.colors.onSurfaceVariant }]}>
                  Invoice #{order?.id}
                </Text>

                <Divider style={styles.divider} />

                <View style={styles.invoiceDetails}>
                  <Text variant="titleMedium" style={{ color: paperTheme.colors.onSurface, marginBottom: 8 }}>
                    Customer: {order?.customer}
                  </Text>
                  <Text variant="bodyMedium" style={{ color: paperTheme.colors.onSurfaceVariant, marginBottom: 4 }}>
                    Date: {new Date(order?.createdAt).toLocaleDateString()}
                  </Text>
                  <Text variant="bodyMedium" style={{ color: paperTheme.colors.onSurfaceVariant, marginBottom: 16 }}>
                    Status: {order?.status}
                  </Text>

                  <Text variant="titleMedium" style={{ color: paperTheme.colors.onSurface, marginBottom: 8 }}>
                    Order Items:
                  </Text>
                  {order?.items?.map((item, index) => (
                    <View key={index} style={styles.itemRow}>
                      <Text style={{ color: paperTheme.colors.onSurface, flex: 1 }}>
                        {item.name} x{item.quantity}
                      </Text>
                      <Text style={{ color: paperTheme.colors.onSurface }}>
                        ${(item.price * item.quantity).toFixed(2)}
                      </Text>
                    </View>
                  ))}

                  <Divider style={styles.divider} />

                  <View style={styles.totalRow}>
                    <Text variant="titleLarge" style={{ color: paperTheme.colors.onSurface }}>
                      Total: ${order?.total?.toFixed(2)}
                    </Text>
                  </View>
                </View>
              </Card.Content>
            </Card>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: paperTheme.colors.outline + '20' }]}>
            <View style={styles.buttonRow}>
              <Button
                mode="outlined"
                onPress={handleGeneratePDF}
                icon="download"
                style={styles.button}
              >
                Download PDF
              </Button>
              <Button
                mode="contained"
                onPress={handlePrint}
                icon="printer"
                style={styles.button}
                disabled={isPrinting}
                loading={isPrinting}
              >
                {isPrinting ? 'Printing...' : 'Print'}
              </Button>
            </View>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  invoiceCard: {
    borderRadius: 12,
  },
  invoiceTitle: {
    textAlign: 'center',
    fontWeight: '700',
    marginBottom: 8,
  },
  invoiceSubtitle: {
    textAlign: 'center',
    marginBottom: 16,
  },
  divider: {
    marginVertical: 16,
  },
  invoiceDetails: {
    paddingVertical: 8,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 4,
  },
  totalRow: {
    alignItems: 'flex-end',
    paddingTop: 8,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default PDFInvoiceBottomSheet;
