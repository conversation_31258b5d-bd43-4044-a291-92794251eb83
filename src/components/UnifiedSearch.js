/**
 * UnifiedSearch - Single search component to replace all search implementations
 * Provides consistent search UX across the entire app
 */

import React, { useState, useCallback, useEffect, useRef, useMemo } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity, Modal } from 'react-native';
import {
  Searchbar,
  Text,
  Surface,
  Chip,
  IconButton,
  useTheme,
  Portal,
  Button,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StorageService } from '../services/storageService';

const UnifiedSearch = ({
  // Search configuration
  type = 'global', // 'global', 'orders', 'products', 'activities', 'customers'
  data = [],
  searchFields = ['name'],
  placeholder = 'Search...',

  // Display options
  mode = 'icon', // 'icon', 'bar', 'modal'
  showSuggestions = true,
  showRecentSearches = true,
  showFilters = false,
  filters = [],

  // Callbacks
  onSearch,
  onResultSelect,
  onFilterChange,

  // Styling
  style,
  iconSize = 24,
  iconColor,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState('All');
  const [showModal, setShowModal] = useState(false);
  const [showDropdown, setShowDropdown] = useState(false);
  const searchTimeoutRef = useRef(null);

  // Load recent searches on mount
  useEffect(() => {
    loadRecentSearches();
  }, [type]);

  const loadRecentSearches = async () => {
    try {
      const storageKey = `recentSearches_${type}`;
      const recent = await StorageService.get(storageKey, true) || [];
      setRecentSearches(recent.slice(0, 5));
    } catch (error) {
      console.warn('Failed to load recent searches:', error);
    }
  };

  const saveRecentSearch = async (searchQuery) => {
    if (!searchQuery.trim() || !showRecentSearches) return;

    try {
      const storageKey = `recentSearches_${type}`;
      const recent = await StorageService.get(storageKey, true) || [];
      const updated = [
        searchQuery,
        ...recent.filter(item => item !== searchQuery)
      ].slice(0, 5);

      await StorageService.set(storageKey, updated);
      setRecentSearches(updated);
    } catch (error) {
      console.warn('Failed to save recent search:', error);
    }
  };

  // Enhanced search algorithm with scoring and fuzzy matching
  const generateSuggestions = useCallback((searchQuery) => {
    if (!searchQuery.trim() || !showSuggestions) {
      setSuggestions([]);
      return;
    }

    const query = searchQuery.toLowerCase();

    // Enhanced search algorithm with scoring
    const scored = data.map(item => {
      let score = 0;
      let matchedFields = [];
      let bestMatch = '';

      searchFields.forEach(field => {
        const value = item[field];
        if (value) {
          const fieldValue = value.toString().toLowerCase();

          // Exact match gets highest score
          if (fieldValue === query) {
            score += 100;
            matchedFields.push(field);
            bestMatch = fieldValue;
          }
          // Starts with query gets high score
          else if (fieldValue.startsWith(query)) {
            score += 50;
            matchedFields.push(field);
            if (!bestMatch) bestMatch = fieldValue;
          }
          // Contains query gets medium score
          else if (fieldValue.includes(query)) {
            score += 25;
            matchedFields.push(field);
            if (!bestMatch) bestMatch = fieldValue;
          }
          // Fuzzy match for typos (simple implementation)
          else if (query.length > 2 && fieldValue.includes(query.slice(0, -1))) {
            score += 10;
            matchedFields.push(field);
            if (!bestMatch) bestMatch = fieldValue;
          }
        }
      });

      return {
        ...item,
        _searchScore: score,
        _matchedFields: matchedFields,
        _bestMatch: bestMatch,
        matchedField: matchedFields[0] // Keep for backward compatibility
      };
    })
    .filter(item => item._searchScore > 0)
    .sort((a, b) => b._searchScore - a._searchScore)
    .slice(0, 8); // Increased from 5 to 8 for better results

    setSuggestions(scored);
  }, [data, searchFields, showSuggestions]);

  const handleQueryChange = (searchQuery) => {
    setQuery(searchQuery);
    setShowDropdown(true);

    // Debounce suggestions generation
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      generateSuggestions(searchQuery);
    }, 300);
  };

  const handleSearch = async (searchQuery = query) => {
    if (!searchQuery.trim()) return;

    setShowDropdown(false);
    setShowModal(false);

    try {
      await saveRecentSearch(searchQuery);
      onSearch?.(searchQuery, selectedFilter);
    } catch (error) {
      console.warn('Search failed:', error);
    }
  };

  const handleSuggestionSelect = (item) => {
    const searchTerm = item.name || item[searchFields[0]] || '';
    setQuery(searchTerm);
    setShowDropdown(false);
    setShowModal(false);
    onResultSelect?.(item);
    saveRecentSearch(searchTerm);
  };

  const handleFilterChange = (filter) => {
    setSelectedFilter(filter);
    onFilterChange?.(filter);
  };

  const clearRecentSearches = async () => {
    try {
      const storageKey = `recentSearches_${type}`;
      await StorageService.remove(storageKey);
      setRecentSearches([]);
    } catch (error) {
      console.warn('Failed to clear recent searches:', error);
    }
  };

  // Get placeholder based on type
  const getPlaceholder = () => {
    switch (type) {
      case 'orders':
        return 'Search orders...';
      case 'products':
        return 'Search products...';
      case 'activities':
        return 'Search activities...';
      case 'customers':
        return 'Search customers...';
      default:
        return placeholder;
    }
  };

  // Get appropriate icon for search result item
  const getSearchIcon = (item) => {
    if (item.price !== undefined) return 'package-variant'; // Product
    if (item.customerName || item.customer) return 'clipboard-text'; // Order
    if (item.action || item.type) return 'history'; // Activity
    if (item.email || item.phone) return 'account'; // Customer
    return 'magnify'; // Default
  };

  // Get color based on search score
  const getScoreColor = (score) => {
    if (score >= 80) return theme.colors.tertiary; // Green for high relevance
    if (score >= 50) return theme.colors.secondary; // Orange for medium relevance
    return theme.colors.onSurfaceVariant; // Gray for low relevance
  };

  // Render search icon (for icon mode)
  const renderSearchIcon = () => (
    <IconButton
      icon="magnify"
      size={iconSize}
      iconColor={iconColor || theme.colors.onSurface}
      onPress={() => setShowModal(true)}
      style={style}
    />
  );

  // Render search bar (for bar mode)
  const renderSearchBar = () => (
    <View style={[styles.searchBarContainer, style]}>
      <Searchbar
        placeholder={getPlaceholder()}
        onChangeText={handleQueryChange}
        value={query}
        onSubmitEditing={() => handleSearch()}
        style={[
          styles.searchBar,
          {
            backgroundColor: theme.colors.surfaceVariant,
            borderColor: theme.colors.outline,
            borderWidth: 1,
          }
        ]}
        inputStyle={{ color: theme.colors.onSurface }}
        placeholderTextColor={theme.colors.onSurfaceVariant}
        iconColor={theme.colors.onSurfaceVariant}
        onFocus={() => setShowDropdown(true)}
        onBlur={() => setTimeout(() => setShowDropdown(false), 200)}
      />

      {showDropdown && (showRecentSearches || suggestions.length > 0) && (
        <Surface
          style={[
            styles.dropdown,
            {
              backgroundColor: theme.colors.surface,
              borderColor: theme.colors.outline,
              borderWidth: 1,
            }
          ]}
          elevation={4}
        >
          {renderDropdownContent()}
        </Surface>
      )}
    </View>
  );

  // Render dropdown content
  const renderDropdownContent = () => (
    <>
      {showRecentSearches && recentSearches.length > 0 && (
        <View>
          <View style={styles.sectionHeader}>
            <Text variant="labelMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              Recent Searches
            </Text>
            <TouchableOpacity onPress={clearRecentSearches}>
              <Text variant="labelSmall" style={{ color: theme.colors.primary }}>
                Clear
              </Text>
            </TouchableOpacity>
          </View>
          {recentSearches.map((search, index) => (
            <TouchableOpacity
              key={index}
              style={styles.dropdownItem}
              onPress={() => {
                setQuery(search);
                handleSearch(search);
              }}
            >
              <Icon name="history" size={16} color={theme.colors.onSurfaceVariant} />
              <Text variant="bodyMedium" style={styles.dropdownText}>
                {search}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      )}

      {suggestions.length > 0 && (
        <View>
          {recentSearches.length > 0 && (
            <View style={[styles.separator, { backgroundColor: theme.colors.outline + '30' }]} />
          )}
          <View style={styles.sectionHeader}>
            <Text variant="labelMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              Suggestions
            </Text>
          </View>
          {suggestions.map((item) => {
            const displayText = item.name || item[searchFields[0]] || 'Unknown';
            const matchedField = item._matchedFields?.[0] || searchFields[0];
            const fieldValue = item[matchedField];

            return (
              <TouchableOpacity
                key={item.id || item.name || Math.random()}
                style={styles.dropdownItem}
                onPress={() => handleSuggestionSelect(item)}
              >
                <Icon
                  name={getSearchIcon(item)}
                  size={16}
                  color={theme.colors.onSurfaceVariant}
                />
                <View style={styles.dropdownText}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                    {displayText}
                  </Text>
                  {fieldValue && fieldValue !== displayText && (
                    <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 2 }}>
                      in {matchedField}: {fieldValue.toString()}
                    </Text>
                  )}
                </View>
                {item._searchScore && (
                  <View style={[styles.scoreIndicator, { backgroundColor: getScoreColor(item._searchScore) }]}>
                    <Text variant="labelSmall" style={{
                      color: theme.colors.onPrimary,
                      fontSize: 10,
                      fontWeight: '600'
                    }}>
                      {Math.round(item._searchScore)}
                    </Text>
                  </View>
                )}
              </TouchableOpacity>
            );
          })}
        </View>
      )}
    </>
  );

  // Render search modal (for modal mode)
  const renderSearchModal = () => (
    <Portal>
      <Modal
        visible={showModal}
        onDismiss={() => setShowModal(false)}
        contentContainerStyle={[
          styles.modal,
          {
            backgroundColor: theme.colors.surface,
            marginTop: insets.top + 20,
            borderColor: theme.colors.outline,
          }
        ]}
      >
        <View style={[styles.modalHeader, { borderBottomColor: theme.colors.outline + '20' }]}>
          <Text variant="titleLarge" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
            Search {type.charAt(0).toUpperCase() + type.slice(1)}
          </Text>
          <IconButton
            icon="close"
            size={24}
            onPress={() => setShowModal(false)}
            iconColor={theme.colors.onSurfaceVariant}
            style={{ backgroundColor: theme.colors.surfaceVariant }}
          />
        </View>

        <Searchbar
          placeholder={getPlaceholder()}
          onChangeText={handleQueryChange}
          value={query}
          onSubmitEditing={() => handleSearch()}
          style={[
            styles.modalSearchBar,
            {
              backgroundColor: theme.colors.surfaceVariant,
              borderColor: theme.colors.outline,
            }
          ]}
          inputStyle={{ color: theme.colors.onSurface }}
          placeholderTextColor={theme.colors.onSurfaceVariant}
          iconColor={theme.colors.onSurfaceVariant}
          autoFocus
        />

        {showFilters && filters.length > 0 && (
          <View style={styles.filtersContainer}>
            {filters.map((filter) => (
              <Chip
                key={filter}
                selected={selectedFilter === filter}
                onPress={() => handleFilterChange(filter)}
                style={[
                  styles.filterChip,
                  {
                    backgroundColor: selectedFilter === filter
                      ? theme.colors.primaryContainer
                      : theme.colors.surface,
                    borderColor: selectedFilter === filter
                      ? theme.colors.primary
                      : theme.colors.outline,
                  }
                ]}
                textStyle={{
                  color: selectedFilter === filter
                    ? theme.colors.onPrimaryContainer
                    : theme.colors.onSurface,
                }}
                mode={selectedFilter === filter ? 'flat' : 'outlined'}
              >
                {filter}
              </Chip>
            ))}
          </View>
        )}

        <View style={[styles.modalContent, { backgroundColor: theme.colors.background }]}>
          {renderDropdownContent()}
        </View>

        <View style={[styles.modalActions, { borderTopColor: theme.colors.outline + '20' }]}>
          <Button
            mode="outlined"
            onPress={() => setShowModal(false)}
            style={[styles.modalButton, { borderColor: theme.colors.outline }]}
            textColor={theme.colors.onSurface}
          >
            Cancel
          </Button>
          <Button
            mode="contained"
            onPress={() => handleSearch()}
            style={styles.modalButton}
            disabled={!query.trim()}
            buttonColor={theme.colors.primary}
            textColor={theme.colors.onPrimary}
          >
            Search
          </Button>
        </View>
      </Modal>
    </Portal>
  );

  // Render based on mode
  switch (mode) {
    case 'icon':
      return (
        <>
          {renderSearchIcon()}
          {renderSearchModal()}
        </>
      );
    case 'bar':
      return renderSearchBar();
    case 'modal':
      return renderSearchModal();
    default:
      return renderSearchIcon();
  }
};

const styles = StyleSheet.create({
  searchBarContainer: {
    position: 'relative',
  },
  searchBar: {
    borderRadius: 12,
    elevation: 2,
  },
  dropdown: {
    position: 'absolute',
    top: '100%',
    left: 0,
    right: 0,
    zIndex: 1000,
    borderRadius: 12,
    marginTop: 4,
    maxHeight: 300,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  dropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    marginHorizontal: 8,
    marginVertical: 2,
  },
  dropdownText: {
    marginLeft: 12,
    flex: 1,
  },
  scoreIndicator: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    minWidth: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  separator: {
    height: 1,
    marginVertical: 8,
  },
  modal: {
    margin: 20,
    borderRadius: 16,
    padding: 20,
    maxHeight: '80%',
    borderWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
  },
  modalSearchBar: {
    marginBottom: 16,
    borderRadius: 12,
  },
  filtersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  filterChip: {
    marginRight: 8,
    marginBottom: 8,
  },
  modalContent: {
    flex: 1,
    marginBottom: 16,
  },
  modalActions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    gap: 12,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  modalButton: {
    minWidth: 80,
  },
});

export default UnifiedSearch;
