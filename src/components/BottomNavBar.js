/**
 * BottomNavBar - Complete navigation bar with Dashboard, Scan, Plus, Orders, Settings
 * Features floating plus button and active state management
 */

import React from 'react';
import { View, TouchableOpacity, StyleSheet, Alert } from 'react-native';
import { Text, useTheme, Surface } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

const BottomNavBar = ({
  navigation,
  currentRoute,
  onPlusPress,
  style,
  backgroundColor,
  elevation = 8,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const tabs = [
    {
      name: 'Dashboard',
      icon: 'view-dashboard',
      iconOutline: 'view-dashboard-outline',
      label: 'Dashboard',
      route: 'Dashboard'
    },
    {
      name: 'Scan',
      icon: 'qrcode-scan',
      iconOutline: 'qrcode-scan',
      label: 'Scan',
      route: 'Scan'
    },
    {
      name: 'plus',
      icon: 'plus',
      label: 'Add',
      isPlus: true
    },
    {
      name: 'Orders',
      icon: 'clipboard-list',
      iconOutline: 'clipboard-list-outline',
      label: 'Orders',
      route: 'Orders'
    },
    {
      name: 'Setting<PERSON>',
      icon: 'cog',
      iconOutline: 'cog-outline',
      label: 'Settings',
      route: 'Settings'
    },
  ];

  const handleTabPress = (tab) => {
    if (tab.isPlus) {
      if (onPlusPress) {
        onPlusPress();
      } else {
        // Default plus action - show quick add menu
        Alert.alert(
          'Quick Add',
          'What would you like to add?',
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'New Product',
              onPress: () => navigation?.navigate('Products'),
            },
            {
              text: 'New Order',
              onPress: () => navigation?.navigate('Orders'),
            },
            {
              text: 'Scan Item',
              onPress: () => navigation?.navigate('Scan'),
            },
          ]
        );
      }
      return;
    }

    // Regular tab navigation
    if (navigation && tab.route) {
      if (tab.route === 'Settings') {
        navigation.getParent()?.navigate('SettingsStack');
      } else {
        navigation.navigate(tab.route);
      }
    }
  };

  const isTabActive = (tabName) => {
    // Handle nested navigation routes (like Settings stack)
    if (tabName === 'Settings') {
      return currentRoute === 'Settings' ||
             currentRoute === 'SettingsMain' ||
             currentRoute === 'Profile' ||
             currentRoute === 'Reports';
    }
    return currentRoute === tabName || currentRoute === tabName.replace('Main', '');
  };

  const navBarBackgroundColor = backgroundColor || theme.colors.surface;

  return (
    <Surface
      style={[
        styles.container,
        {
          backgroundColor: navBarBackgroundColor,
          paddingBottom: insets.bottom,
          borderTopColor: theme.colors.outline + '20'
        },
        style
      ]}
      elevation={elevation}
    >
      {tabs.map((tab) => {
        const isActive = isTabActive(tab.name);
        const isPlusTab = tab.isPlus;

        return (
          <TouchableOpacity
            key={tab.name}
            style={[
              styles.tab,
              isPlusTab && styles.plusTab,
              isPlusTab && { backgroundColor: theme.colors.primary }
            ]}
            onPress={() => handleTabPress(tab)}
            activeOpacity={0.7}
          >
            {isPlusTab ? (
              <View style={styles.plusIconContainer}>
                <Icon
                  name={tab.icon}
                  size={28}
                  color={theme.colors.onPrimary}
                />
              </View>
            ) : (
              <>
                <Icon
                  name={isActive ? tab.icon : (tab.iconOutline || tab.icon)}
                  size={24}
                  color={isActive ? theme.colors.primary : theme.colors.onSurfaceVariant}
                />
                <Text
                  variant="labelSmall"
                  style={{
                    color: isActive ? theme.colors.primary : theme.colors.onSurfaceVariant,
                    marginTop: 4,
                    fontWeight: isActive ? '600' : '400'
                  }}
                >
                  {tab.label}
                </Text>
              </>
            )}
          </TouchableOpacity>
        );
      })}
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 80,
    paddingTop: 8,
    paddingHorizontal: 4,
    borderTopWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 2,
  },
  plusTab: {
    width: 56,
    height: 56,
    borderRadius: 28,
    marginTop: -20,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  plusIconContainer: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default BottomNavBar;
