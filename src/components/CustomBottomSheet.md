# CustomBottomSheet Component

A comprehensive, reusable bottom sheet component that provides consistent styling, behavior, and functionality across the entire app.

## Features

- ✅ **Consistent Design** - Unified header, content, and footer layout
- ✅ **Flexible Configuration** - Customizable headers, icons, titles, and footers
- ✅ **Automatic Keyboard Handling** - Smart keyboard behavior and dismissal
- ✅ **Safe Area Support** - Proper padding for different devices
- ✅ **Scrollable Content** - Optional scrollable content area
- ✅ **Theme Integration** - Automatic theme color support
- ✅ **Accessibility** - Built-in accessibility features

## Basic Usage

```jsx
import CustomBottomSheet from './CustomBottomSheet';

const MyBottomSheet = ({ bottomSheetRef }) => {
  return (
    <CustomBottomSheet
      ref={bottomSheetRef}
      snapPoints={['50%', '80%']}
      title="My Bottom Sheet"
      icon="plus"
    >
      <Text>Your content here</Text>
    </CustomBottomSheet>
  );
};
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `snapPoints` | `string[]` | `['50%']` | Array of snap points |
| `index` | `number` | `-1` | Initial index (-1 for closed) |
| `title` | `string` | - | Header title |
| `subtitle` | `string` | - | Header subtitle |
| `icon` | `string` | - | Header icon name (MaterialCommunityIcons) |
| `iconColor` | `string` | `theme.colors.primary` | Icon color |
| `iconBackground` | `string` | `iconColor + '15'` | Icon background color |
| `showHeader` | `boolean` | `true` | Show/hide header |
| `showCloseButton` | `boolean` | `true` | Show/hide close button |
| `scrollable` | `boolean` | `true` | Make content scrollable |
| `footerContent` | `ReactNode` | - | Footer content (buttons, etc.) |
| `onClose` | `function` | - | Callback when sheet closes |
| `onChange` | `function` | - | Callback when sheet changes |

## Examples

### Simple Bottom Sheet
```jsx
<CustomBottomSheet
  ref={bottomSheetRef}
  snapPoints={['40%']}
  title="Simple Sheet"
  icon="information"
>
  <Text>Simple content</Text>
</CustomBottomSheet>
```

### Form Bottom Sheet with Footer
```jsx
const footerButtons = (
  <View style={styles.buttonRow}>
    <Button mode="outlined" onPress={onCancel}>Cancel</Button>
    <Button mode="contained" onPress={onSave}>Save</Button>
  </View>
);

<CustomBottomSheet
  ref={bottomSheetRef}
  snapPoints={['60%', '90%']}
  title="Add Product"
  icon="package-variant"
  footerContent={footerButtons}
  onClose={resetForm}
>
  <TextInput label="Product Name" />
  <TextInput label="Price" />
</CustomBottomSheet>
```

### Details Bottom Sheet
```jsx
<CustomBottomSheet
  ref={bottomSheetRef}
  snapPoints={['25%', '80%']}
  title="Order Details"
  subtitle="Order #12345"
  icon="clipboard-list"
  iconColor="#2563EB"
>
  <Text>Order information...</Text>
</CustomBottomSheet>
```

### Custom Header
```jsx
const customHeader = (
  <View style={styles.customHeader}>
    <Text variant="headlineSmall">Custom Header</Text>
    <Chip>Status</Chip>
  </View>
);

<CustomBottomSheet
  ref={bottomSheetRef}
  snapPoints={['50%']}
  headerContent={customHeader}
>
  <Text>Content with custom header</Text>
</CustomBottomSheet>
```

## Migration Guide

### Before (Old Pattern)
```jsx
<CustomBottomSheet
  ref={bottomSheetRef}
  index={-1}
  snapPoints={snapPoints}
  enablePanDownToClose
  backgroundStyle={{ backgroundColor: theme.colors.surface }}
  handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
>
  <BottomSheetView style={styles.container}>
    <View style={styles.header}>
      <Text variant="headlineSmall">Title</Text>
      <IconButton icon="close" onPress={() => ref.current?.close()} />
    </View>
    <BottomSheetScrollView style={styles.content}>
      {/* Content */}
    </BottomSheetScrollView>
  </BottomSheetView>
</CustomBottomSheet>
```

### After (New Pattern)
```jsx
<CustomBottomSheet
  ref={bottomSheetRef}
  snapPoints={snapPoints}
  title="Title"
  icon="plus"
  onClose={handleClose}
>
  {/* Content */}
</CustomBottomSheet>
```

## Benefits

1. **Reduced Code Duplication** - No need to repeat header/footer patterns
2. **Consistent UX** - All bottom sheets look and behave the same
3. **Easier Maintenance** - Changes in one place affect all bottom sheets
4. **Better Accessibility** - Built-in accessibility features
5. **Theme Integration** - Automatic theme color application
6. **Simplified API** - Fewer props to manage

## Best Practices

1. **Use descriptive titles** - Help users understand the sheet's purpose
2. **Choose appropriate icons** - Use MaterialCommunityIcons for consistency
3. **Provide footer buttons** - For forms and actions
4. **Handle onClose** - Reset form state when sheet closes
5. **Use appropriate snap points** - Consider content length
6. **Test on different devices** - Ensure proper safe area handling
