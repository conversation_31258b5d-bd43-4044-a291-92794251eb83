import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  useTheme,
  Card,
  Switch,
  Divider,
} from 'react-native-paper';
import { TimePicker } from 'react-native-paper-dates';

const StoreHoursModal = ({ visible, onDismiss, onSave, storeHours }) => {
  const theme = useTheme();
  const [hours, setHours] = useState({
    monday: { open: '09:00', close: '18:00', closed: false },
    tuesday: { open: '09:00', close: '18:00', closed: false },
    wednesday: { open: '09:00', close: '18:00', closed: false },
    thursday: { open: '09:00', close: '18:00', closed: false },
    friday: { open: '09:00', close: '18:00', closed: false },
    saturday: { open: '09:00', close: '17:00', closed: false },
    sunday: { open: '10:00', close: '16:00', closed: true },
  });

  const [timePickerVisible, setTimePickerVisible] = useState(false);
  const [currentEdit, setCurrentEdit] = useState({ day: '', type: '', time: '' });

  const days = [
    { key: 'monday', label: 'Monday' },
    { key: 'tuesday', label: 'Tuesday' },
    { key: 'wednesday', label: 'Wednesday' },
    { key: 'thursday', label: 'Thursday' },
    { key: 'friday', label: 'Friday' },
    { key: 'saturday', label: 'Saturday' },
    { key: 'sunday', label: 'Sunday' },
  ];

  useEffect(() => {
    if (storeHours) {
      setHours(storeHours);
    }
  }, [storeHours, visible]);

  const handleTimeEdit = (day, type) => {
    const currentTime = hours[day][type];
    const [hour, minute] = currentTime.split(':').map(Number);
    setCurrentEdit({ day, type, time: currentTime });
    setTimePickerVisible(true);
  };

  const handleTimeConfirm = ({ hours: hour, minutes: minute }) => {
    const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    setHours(prev => ({
      ...prev,
      [currentEdit.day]: {
        ...prev[currentEdit.day],
        [currentEdit.type]: timeString
      }
    }));
    setTimePickerVisible(false);
  };

  const toggleDayClosed = (day) => {
    setHours(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        closed: !prev[day].closed
      }
    }));
  };

  const handleSave = () => {
    onSave(hours);
    onDismiss();
  };

  const DayCard = ({ day, label }) => (
    <Card style={styles.dayCard} mode="outlined">
      <Card.Content>
        <View style={styles.dayHeader}>
          <Text variant="titleMedium">{label}</Text>
          <View style={styles.closedToggle}>
            <Text variant="bodyMedium" style={{ marginRight: 8 }}>
              {hours[day].closed ? 'Closed' : 'Open'}
            </Text>
            <Switch
              value={!hours[day].closed}
              onValueChange={() => toggleDayClosed(day)}
              color={theme.colors.primary}
            />
          </View>
        </View>
        
        {!hours[day].closed && (
          <>
            <Divider style={{ marginVertical: 12 }} />
            <View style={styles.timeRow}>
              <View style={styles.timeSection}>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                  Opening Time
                </Text>
                <Button
                  mode="outlined"
                  onPress={() => handleTimeEdit(day, 'open')}
                  style={styles.timeButton}
                >
                  {hours[day].open}
                </Button>
              </View>
              <View style={styles.timeSection}>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                  Closing Time
                </Text>
                <Button
                  mode="outlined"
                  onPress={() => handleTimeEdit(day, 'close')}
                  style={styles.timeButton}
                >
                  {hours[day].close}
                </Button>
              </View>
            </View>
          </>
        )}
      </Card.Content>
    </Card>
  );

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.modal,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
            Store Hours
          </Text>

          {days.map(({ key, label }) => (
            <DayCard key={key} day={key} label={label} />
          ))}

          <View style={styles.buttonRow}>
            <Button
              mode="outlined"
              onPress={onDismiss}
              style={styles.button}
            >
              Cancel
            </Button>
            <Button
              mode="contained"
              onPress={handleSave}
              style={styles.button}
            >
              Save Hours
            </Button>
          </View>
        </ScrollView>
      </Modal>

      {timePickerVisible && (
        <TimePicker
          visible={timePickerVisible}
          onDismiss={() => setTimePickerVisible(false)}
          onConfirm={handleTimeConfirm}
          hours={parseInt(currentEdit.time.split(':')[0])}
          minutes={parseInt(currentEdit.time.split(':')[1])}
        />
      )}
    </Portal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    maxHeight: '90%',
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
  },
  dayCard: {
    marginBottom: 12,
  },
  dayHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  closedToggle: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  timeRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  timeSection: {
    flex: 1,
    marginHorizontal: 4,
  },
  timeButton: {
    marginTop: 8,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 24,
  },
  button: {
    flex: 1,
    marginHorizontal: 8,
  },
});

export default StoreHoursModal;
