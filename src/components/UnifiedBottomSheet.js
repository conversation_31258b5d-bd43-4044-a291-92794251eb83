/**
 * UnifiedBottomSheet - Reusable bottom sheet component
 * Provides consistent design and behavior across all bottom sheets
 */

import React, { useCallback, useMemo, forwardRef } from 'react';
import { View, StyleSheet, Keyboard } from 'react-native';
import {
  Text,
  useTheme,
  IconButton,
  Portal,
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { SPACING, BORDER_RADIUS, COMPONENT_SIZES, SHADOWS, getThemedShadow } from '../theme/designTokens';

const UnifiedBottomSheet = forwardRef(({
  title,
  subtitle,
  icon,
  iconColor,
  iconBackgroundColor,
  children,
  snapPoints = ['50%', '90%'],
  onClose,
  onSheetChange,
  showCloseButton = true,
  enablePanDownToClose = true,
  keyboardBehavior = 'interactive',
  style,
  headerStyle,
  contentStyle,
}, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const defaultSnapPoints = useMemo(() => snapPoints, [snapPoints]);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
      onClose?.();
    }
    onSheetChange?.(index);
  }, [onClose, onSheetChange]);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  const handleClosePress = useCallback(() => {
    ref?.current?.close();
  }, [ref]);

  return (
    <Portal>
      <BottomSheet
        ref={ref}
        index={-1}
        snapPoints={defaultSnapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior={keyboardBehavior}
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={enablePanDownToClose}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={[styles.bottomSheet, style]}
      >
        <BottomSheetView style={styles.container}>
          {/* Header */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }, headerStyle]}>
            <View style={styles.headerLeft}>
              {icon && (
                <View style={[
                  styles.iconContainer,
                  { backgroundColor: iconBackgroundColor || theme.colors.primary + '15' }
                ]}>
                  <Icon
                    name={icon}
                    size={24}
                    color={iconColor || theme.colors.primary}
                  />
                </View>
              )}
              <View style={styles.titleContainer}>
                <Text style={[styles.title, {
                  color: theme.colors.onSurface,
                  fontSize: 18,
                  fontWeight: '700',
                  lineHeight: 24
                }]}>
                  {title}
                </Text>
                {subtitle && (
                  <Text style={[styles.subtitle, {
                    color: theme.colors.onSurfaceVariant,
                    fontSize: 14,
                    fontWeight: '400',
                    lineHeight: 20,
                    marginTop: 2
                  }]}>
                    {subtitle}
                  </Text>
                )}
              </View>
            </View>
            {showCloseButton && (
              <IconButton
                icon="close"
                size={20}
                onPress={handleClosePress}
                iconColor={theme.colors.onSurfaceVariant}
              />
            )}
          </View>

          {/* Content */}
          <View style={[styles.content, contentStyle]}>
            {children}
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    ...SHADOWS.lg,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: SPACING.md,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: BORDER_RADIUS.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: SPACING.xs / 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
  },
});

UnifiedBottomSheet.displayName = 'UnifiedBottomSheet';

export default UnifiedBottomSheet;
