/**
 * UnifiedBottomSheet - Single bottom sheet component inspired by QuickActionsBottomSheet
 * Handles all bottom sheet functionality across the app with consistent UX
 */

import React, { forwardRef, useCallback, useMemo, useState } from 'react';
import { View, StyleSheet, ScrollView, FlatList, TouchableOpacity } from 'react-native';
import {
  Text,
  Button,
  Surface,
  IconButton,
  Searchbar,
  Chip,
  TextInput,
  Switch,
  useTheme,
  Divider,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
// CustomBottomSheet removed - using direct BottomSheet implementation

const UnifiedBottomSheet = forwardRef((props, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const {
    type = 'quick-actions', // 'quick-actions', 'form', 'details', 'list', 'search'
    title,
    subtitle,
    icon,
    data = [],
    actions = [],
    searchable = false,
    onAction,
    onClose,
    snapPoints = ['50%', '80%'],
    ...otherProps
  } = props;

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedItems, setSelectedItems] = useState([]);

  // Filter data based on search query
  const filteredData = useMemo(() => {
    if (!searchQuery || !searchable) return data;
    return data.filter(item =>
      item.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      item.name?.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [data, searchQuery, searchable]);

  const handleAction = useCallback((action, item = null) => {
    onAction?.(action, item);
    if (action.closeOnPress !== false) {
      ref.current?.close();
    }
  }, [onAction, ref]);

  // Quick Actions Layout (inspired by your favorite QuickActionsBottomSheet)
  const renderQuickActions = () => (
    <View style={styles.quickActionsContainer}>
      <View style={styles.actionsGrid}>
        {actions.map((action, index) => (
          <TouchableOpacity
            key={action.id || index}
            style={[
              styles.actionItem,
              { backgroundColor: theme.colors.surfaceVariant }
            ]}
            onPress={() => handleAction(action)}
            activeOpacity={0.7}
          >
            <View style={[
              styles.actionIconContainer,
              { backgroundColor: action.color || theme.colors.primary }
            ]}>
              <Icon
                name={action.icon}
                size={24}
                color={theme.colors.onPrimary}
              />
            </View>
            <Text variant="labelMedium" style={styles.actionLabel}>
              {action.title}
            </Text>
            {action.description && (
              <Text variant="bodySmall" style={[styles.actionDescription, { color: theme.colors.onSurfaceVariant }]}>
                {action.description}
              </Text>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  );

  // Form Layout
  const renderForm = () => (
    <ScrollView style={styles.formContainer} showsVerticalScrollIndicator={false}>
      {data.map((field, index) => (
        <View key={field.id || index} style={styles.formField}>
          {field.type === 'text' && (
            <TextInput
              label={field.label}
              value={field.value}
              onChangeText={field.onChange}
              mode="outlined"
              error={field.error}
              {...field.props}
            />
          )}
          {field.type === 'switch' && (
            <View style={styles.switchRow}>
              <Text variant="bodyLarge">{field.label}</Text>
              <Switch
                value={field.value}
                onValueChange={field.onChange}
              />
            </View>
          )}
          {field.type === 'chips' && (
            <View>
              <Text variant="labelMedium" style={styles.fieldLabel}>{field.label}</Text>
              <View style={styles.chipsContainer}>
                {field.options?.map((option) => (
                  <Chip
                    key={option.value}
                    selected={field.value === option.value}
                    onPress={() => field.onChange(option.value)}
                    style={styles.chip}
                  >
                    {option.label}
                  </Chip>
                ))}
              </View>
            </View>
          )}
          {field.type === 'custom' && field.render && (
            <View>
              {field.render()}
            </View>
          )}
        </View>
      ))}
    </ScrollView>
  );

  // List Layout
  const renderList = () => (
    <FlatList
      data={filteredData}
      keyExtractor={(item) => item.id?.toString() || item.name}
      renderItem={({ item }) => (
        <TouchableOpacity
          style={[styles.listItem, { backgroundColor: theme.colors.surface }]}
          onPress={() => handleAction({ type: 'select' }, item)}
        >
          {item.icon && (
            <View style={[styles.listIcon, { backgroundColor: theme.colors.primaryContainer }]}>
              <Icon name={item.icon} size={20} color={theme.colors.primary} />
            </View>
          )}
          <View style={styles.listContent}>
            <Text variant="titleMedium">{item.title || item.name}</Text>
            {item.description && (
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                {item.description}
              </Text>
            )}
          </View>
          {item.value && (
            <Text variant="labelMedium" style={{ color: theme.colors.primary }}>
              {item.value}
            </Text>
          )}
        </TouchableOpacity>
      )}
      showsVerticalScrollIndicator={false}
    />
  );

  // Details Layout
  const renderDetails = () => (
    <ScrollView style={styles.detailsContainer} showsVerticalScrollIndicator={false}>
      {data.map((section, index) => (
        <Surface key={index} style={styles.detailsSection} elevation={1}>
          {section.title && (
            <Text variant="titleMedium" style={styles.sectionTitle}>
              {section.title}
            </Text>
          )}
          {section.items?.map((item, itemIndex) => (
            <View key={itemIndex} style={styles.detailItem}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                {item.label}
              </Text>
              <Text variant="bodyLarge">{item.value}</Text>
            </View>
          ))}
        </Surface>
      ))}
    </ScrollView>
  );

  const renderContent = () => {
    switch (type) {
      case 'quick-actions':
        return renderQuickActions();
      case 'form':
        return renderForm();
      case 'list':
        return renderList();
      case 'details':
        return renderDetails();
      default:
        return renderQuickActions();
    }
  };

  const getTitle = () => {
    switch (type) {
      case 'quick-actions':
        return title || 'Quick Actions';
      case 'form':
        return title || 'Form';
      case 'list':
        return title || 'Select Item';
      case 'details':
        return title || 'Details';
      default:
        return title || 'Options';
    }
  };

  const getIcon = () => {
    switch (type) {
      case 'quick-actions':
        return icon || 'lightning-bolt';
      case 'form':
        return icon || 'form-select';
      case 'list':
        return icon || 'format-list-bulleted';
      case 'details':
        return icon || 'information';
      default:
        return icon || 'menu';
    }
  };

  return (
    <CustomBottomSheet
      ref={ref}
      snapPoints={snapPoints}
      title={getTitle()}
      subtitle={subtitle}
      icon={getIcon()}
      onClose={onClose}
      {...otherProps}
    >
      {searchable && (
        <View style={styles.searchContainer}>
          <Searchbar
            placeholder="Search..."
            onChangeText={setSearchQuery}
            value={searchQuery}
            style={styles.searchBar}
          />
        </View>
      )}

      {renderContent()}
    </CustomBottomSheet>
  );
});

const styles = StyleSheet.create({
  // Quick Actions Styles (inspired by your favorite)
  quickActionsContainer: {
    padding: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionItem: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  actionIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 8,
  },
  actionLabel: {
    textAlign: 'center',
    fontWeight: '600',
  },
  actionDescription: {
    textAlign: 'center',
    marginTop: 4,
  },

  // Form Styles
  formContainer: {
    padding: 16,
  },
  formField: {
    marginBottom: 16,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  fieldLabel: {
    marginBottom: 8,
  },
  chipsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  chip: {
    marginBottom: 4,
  },

  // List Styles
  listItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 12,
  },
  listIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  listContent: {
    flex: 1,
  },

  // Details Styles
  detailsContainer: {
    padding: 16,
  },
  detailsSection: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  sectionTitle: {
    marginBottom: 12,
    fontWeight: '600',
  },
  detailItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },

  // Search Styles
  searchContainer: {
    paddingHorizontal: 16,
    paddingBottom: 16,
  },
  searchBar: {
    borderRadius: 12,
  },
});

UnifiedBottomSheet.displayName = 'UnifiedBottomSheet';

export default UnifiedBottomSheet;
