import React, { forwardRef, useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import { Text, TextInput, Button, Avatar } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CustomBottomSheet from './CustomBottomSheet';
import { useTheme } from '../context/ThemeContext';

const EditProfileBottomSheet = forwardRef(({ profile, onSave, onClose }, ref) => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();

  const [formData, setFormData] = useState({
    storeName: '',
    ownerName: '',
    email: '',
    phone: '',
    address: '',
    taxRate: '',
  });

  useEffect(() => {
    if (profile) {
      setFormData({
        storeName: profile.storeName || '',
        ownerName: profile.ownerName || '',
        email: profile.email || '',
        phone: profile.phone || '',
        address: profile.address || '',
        taxRate: (profile.taxRate * 100).toString() || '8',
      });
    }
  }, [profile]);

  const handleSave = () => {
    try {
      const updatedProfile = {
        ...formData,
        taxRate: parseFloat(formData.taxRate) / 100 || 0.08,
      };
      onSave(updatedProfile);
      onClose();
    } catch (error) {
      console.error('Error saving profile:', error);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <CustomBottomSheet
      ref={ref}
      snapPoints={['90%']}
      onClose={onClose}
    >
      <View style={styles.header}>
        <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
          Edit Profile
        </Text>
        <Button
          mode="text"
          onPress={onClose}
          icon="close"
          textColor={theme.colors.onSurfaceVariant}
        >
          Close
        </Button>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Avatar Section */}
        <View style={styles.avatarSection}>
          <View style={[styles.avatarContainer, { backgroundColor: theme.colors.primary + '15' }]}>
            <Avatar.Text
              size={80}
              label={formData.storeName.split(' ').map(word => word[0]).join('').substring(0, 2)}
              style={{ backgroundColor: theme.colors.primary }}
            />
          </View>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 8 }}>
            Store Avatar
          </Text>
        </View>

        {/* Form Fields */}
        <View style={styles.form}>
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Store Information
          </Text>

          <TextInput
            label="Store Name"
            value={formData.storeName}
            onChangeText={(value) => handleInputChange('storeName', value)}
            mode="outlined"
            style={styles.input}
            left={<TextInput.Icon icon="store" />}
          />

          <TextInput
            label="Owner Name"
            value={formData.ownerName}
            onChangeText={(value) => handleInputChange('ownerName', value)}
            mode="outlined"
            style={styles.input}
            left={<TextInput.Icon icon="account" />}
          />

          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Contact Information
          </Text>

          <TextInput
            label="Email Address"
            value={formData.email}
            onChangeText={(value) => handleInputChange('email', value)}
            mode="outlined"
            style={styles.input}
            keyboardType="email-address"
            autoCapitalize="none"
            left={<TextInput.Icon icon="email" />}
          />

          <TextInput
            label="Phone Number"
            value={formData.phone}
            onChangeText={(value) => handleInputChange('phone', value)}
            mode="outlined"
            style={styles.input}
            keyboardType="phone-pad"
            left={<TextInput.Icon icon="phone" />}
          />

          <TextInput
            label="Address"
            value={formData.address}
            onChangeText={(value) => handleInputChange('address', value)}
            mode="outlined"
            style={styles.input}
            multiline
            numberOfLines={3}
            left={<TextInput.Icon icon="map-marker" />}
          />

          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Business Settings
          </Text>

          <TextInput
            label="Tax Rate (%)"
            value={formData.taxRate}
            onChangeText={(value) => handleInputChange('taxRate', value)}
            mode="outlined"
            style={styles.input}
            keyboardType="numeric"
            left={<TextInput.Icon icon="percent" />}
            right={<TextInput.Affix text="%" />}
          />
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={[styles.actions, { paddingBottom: insets.bottom }]}>
        <Button
          mode="outlined"
          onPress={onClose}
          style={styles.actionButton}
        >
          Cancel
        </Button>
        <Button
          mode="contained"
          onPress={handleSave}
          style={styles.actionButton}
          icon="check"
        >
          Save Changes
        </Button>
      </View>
    </CustomBottomSheet>
  );
});

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 14,
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  title: {
    fontWeight: '700',
  },
  content: {
    flex: 1,
    paddingHorizontal: 14,
    paddingTop: 12,
  },
  avatarSection: {
    alignItems: 'center',
    marginBottom: 24,
  },
  avatarContainer: {
    borderRadius: 50,
    padding: 8,
  },
  form: {
    flex: 1,
  },
  sectionTitle: {
    fontWeight: '600',
    marginBottom: 12,
    marginTop: 16,
  },
  input: {
    marginBottom: 12,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 14,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.05)',
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 6,
  },
});

export default EditProfileBottomSheet;
