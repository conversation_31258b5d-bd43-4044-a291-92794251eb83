import React, { forwardRef, useImperativeHandle, useRef, useMemo, useCallback } from 'react';
import { View, StyleSheet, Dimensions, ScrollView, Pressable } from 'react-native';
import { Text, Surface, IconButton, Divider, useTheme } from 'react-native-paper';
import BottomSheet, { BottomSheetBackdrop, BottomSheetScrollView } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import LoggingService from '../services/LoggingService';

const { height: SCREEN_HEIGHT } = Dimensions.get('window');

export interface BottomSheetAction {
  id: string;
  label: string;
  icon?: string;
  onPress: () => void;
  variant?: 'primary' | 'secondary' | 'danger';
  disabled?: boolean;
}

export interface BottomSheetConfig {
  title: string;
  subtitle?: string;
  content: React.ReactNode;
  actions?: BottomSheetAction[];
  enableDynamicSizing?: boolean;
  maxHeight?: number;
  showHandle?: boolean;
  enablePanDownToClose?: boolean;
  enableContentPanningGesture?: boolean;
}

export interface UnifiedBottomSheetRef {
  present: (config: BottomSheetConfig) => void;
  dismiss: () => void;
  snapToIndex: (index: number) => void;
}

interface UnifiedBottomSheetProps {
  onDismiss?: () => void;
}

const UnifiedBottomSheet = forwardRef<UnifiedBottomSheetRef, UnifiedBottomSheetProps>(
  ({ onDismiss }, ref) => {
    const theme = useTheme();
    const insets = useSafeAreaInsets();
    const bottomSheetRef = useRef<BottomSheet>(null);
    const [config, setConfig] = React.useState<BottomSheetConfig | null>(null);

    // Dynamic snap points based on content
    const snapPoints = useMemo(() => {
      if (!config) return ['25%'];
      
      if (config.enableDynamicSizing) {
        const maxHeight = config.maxHeight || SCREEN_HEIGHT * 0.9;
        const minHeight = SCREEN_HEIGHT * 0.25;
        return [minHeight, maxHeight];
      }
      
      return ['25%', '50%', '90%'];
    }, [config]);

    const present = useCallback((newConfig: BottomSheetConfig) => {
      LoggingService.debug('BottomSheet presenting', 'UI', { title: newConfig.title });
      setConfig(newConfig);
      bottomSheetRef.current?.snapToIndex(1);
    }, []);

    const dismiss = useCallback(() => {
      LoggingService.debug('BottomSheet dismissing', 'UI');
      bottomSheetRef.current?.close();
    }, []);

    const snapToIndex = useCallback((index: number) => {
      bottomSheetRef.current?.snapToIndex(index);
    }, []);

    useImperativeHandle(ref, () => ({
      present,
      dismiss,
      snapToIndex,
    }));

    const handleSheetChanges = useCallback((index: number) => {
      if (index === -1) {
        setConfig(null);
        onDismiss?.();
      }
    }, [onDismiss]);

    const renderBackdrop = useCallback(
      (props: any) => (
        <BottomSheetBackdrop
          {...props}
          disappearsOnIndex={-1}
          appearsOnIndex={0}
          opacity={0.5}
          pressBehavior="close"
        />
      ),
      []
    );

    const renderHeader = useCallback(() => {
      if (!config) return null;

      return (
        <Surface style={[styles.header, { backgroundColor: theme.colors.surface }]} elevation={0}>
          <View style={styles.headerContent}>
            <View style={styles.headerText}>
              <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                {config.title}
              </Text>
              {config.subtitle && (
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  {config.subtitle}
                </Text>
              )}
            </View>
            <IconButton
              icon="close"
              size={24}
              onPress={dismiss}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>
          <Divider style={{ marginTop: 8 }} />
        </Surface>
      );
    }, [config, theme, dismiss]);

    const renderActions = useCallback(() => {
      if (!config?.actions?.length) return null;

      return (
        <Surface style={[styles.actionsContainer, { backgroundColor: theme.colors.surface }]} elevation={0}>
          <Divider style={{ marginBottom: 16 }} />
          <View style={styles.actions}>
            {config.actions.map((action, index) => (
              <Pressable
                key={action.id}
                style={[
                  styles.actionButton,
                  {
                    backgroundColor: action.variant === 'primary' 
                      ? theme.colors.primary 
                      : action.variant === 'danger'
                      ? theme.colors.error
                      : theme.colors.surfaceVariant,
                  },
                  action.disabled && styles.actionButtonDisabled,
                ]}
                onPress={action.onPress}
                disabled={action.disabled}
                android_ripple={{ color: theme.colors.onSurface }}
              >
                <Text
                  variant="labelLarge"
                  style={[
                    styles.actionButtonText,
                    {
                      color: action.variant === 'primary' || action.variant === 'danger'
                        ? theme.colors.onPrimary
                        : theme.colors.onSurfaceVariant,
                    },
                    action.disabled && { color: theme.colors.outline },
                  ]}
                >
                  {action.label}
                </Text>
              </Pressable>
            ))}
          </View>
          <View style={{ height: insets.bottom }} />
        </Surface>
      );
    }, [config, theme, insets.bottom]);

    if (!config) return null;

    return (
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        enablePanDownToClose={config.enablePanDownToClose ?? true}
        enableContentPanningGesture={config.enableContentPanningGesture ?? true}
        handleIndicatorStyle={{
          backgroundColor: theme.colors.onSurfaceVariant,
          display: config.showHandle === false ? 'none' : 'flex',
        }}
        backgroundStyle={{
          backgroundColor: theme.colors.surface,
        }}
        style={{
          shadowColor: theme.colors.shadow,
          shadowOffset: { width: 0, height: -4 },
          shadowOpacity: 0.1,
          shadowRadius: 8,
          elevation: 8,
        }}
      >
        {renderHeader()}
        <BottomSheetScrollView
          style={[styles.content, { backgroundColor: theme.colors.surface }]}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          {config.content}
        </BottomSheetScrollView>
        {renderActions()}
      </BottomSheet>
    );
  }
);

const styles = StyleSheet.create({
  header: {
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 0,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  headerText: {
    flex: 1,
    marginRight: 8,
  },
  title: {
    fontWeight: '600',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  actionsContainer: {
    paddingHorizontal: 16,
    paddingTop: 0,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 16,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 48,
  },
  actionButtonDisabled: {
    opacity: 0.5,
  },
  actionButtonText: {
    fontWeight: '600',
  },
});

UnifiedBottomSheet.displayName = 'UnifiedBottomSheet';

export default UnifiedBottomSheet;
