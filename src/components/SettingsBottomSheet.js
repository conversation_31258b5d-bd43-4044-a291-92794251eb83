/**
 * Settings BottomSheet - Example using CustomBottomSheet
 * Shows app settings and preferences
 */

import React, { forwardRef, useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { 
  Text, 
  Switch, 
  Surface, 
  useTheme,
  TouchableRipple,
  Divider,
  Button 
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import CustomBottomSheet from './CustomBottomSheet';

const SettingsBottomSheet = forwardRef((props, ref) => {
  const theme = useTheme();
  const bottomSheetRef = React.useRef(null);
  
  const [settings, setSettings] = useState({
    notifications: true,
    darkMode: false,
    autoBackup: true,
    soundEffects: true,
    analytics: false,
  });

  React.useImperativeHandle(ref, () => ({
    open: () => bottomSheetRef.current?.expand(),
    close: () => bottomSheetRef.current?.close()
  }));

  const toggleSetting = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handleSave = () => {
    console.log('Saving settings:', settings);
    bottomSheetRef.current?.close();
  };

  const handleReset = () => {
    setSettings({
      notifications: true,
      darkMode: false,
      autoBackup: true,
      soundEffects: true,
      analytics: false,
    });
  };

  const footerActions = (
    <View style={styles.footerActions}>
      <Button 
        mode="outlined" 
        onPress={handleReset}
        style={styles.actionButton}
      >
        Reset
      </Button>
      <Button 
        mode="contained" 
        onPress={handleSave}
        style={styles.actionButton}
      >
        Save Changes
      </Button>
    </View>
  );

  const renderSettingItem = (key, title, subtitle, icon) => (
    <View key={key}>
      <TouchableRipple onPress={() => toggleSetting(key)}>
        <View style={styles.settingItem}>
          <View style={styles.settingLeft}>
            <View style={[styles.settingIcon, { backgroundColor: theme.colors.primaryContainer }]}>
              <Icon name={icon} size={20} color={theme.colors.primary} />
            </View>
            <View style={styles.settingText}>
              <Text variant="titleMedium" style={[styles.settingTitle, { color: theme.colors.onSurface }]}>
                {title}
              </Text>
              <Text variant="bodySmall" style={[styles.settingSubtitle, { color: theme.colors.onSurfaceVariant }]}>
                {subtitle}
              </Text>
            </View>
          </View>
          <Switch
            value={settings[key]}
            onValueChange={() => toggleSetting(key)}
          />
        </View>
      </TouchableRipple>
      <Divider style={styles.divider} />
    </View>
  );

  return (
    <CustomBottomSheet
      ref={bottomSheetRef}
      snapPoints={['60%', '90%']}
      title="Settings"
      subtitle="App preferences and configuration"
      icon="cog"
      iconColor={theme.colors.primary}
      footerContent={footerActions}
    >
      {/* App Settings */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <View style={styles.sectionHeader}>
          <Icon name="application-cog" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>App Settings</Text>
        </View>
        
        {renderSettingItem(
          'notifications',
          'Push Notifications',
          'Receive alerts for new orders and updates',
          'bell'
        )}
        
        {renderSettingItem(
          'darkMode',
          'Dark Mode',
          'Use dark theme for better night viewing',
          'theme-light-dark'
        )}
        
        {renderSettingItem(
          'soundEffects',
          'Sound Effects',
          'Play sounds for actions and notifications',
          'volume-high'
        )}
      </Surface>

      {/* Data & Privacy */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surface }]} elevation={1}>
        <View style={styles.sectionHeader}>
          <Icon name="shield-account" size={20} color={theme.colors.secondary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Data & Privacy</Text>
        </View>
        
        {renderSettingItem(
          'autoBackup',
          'Auto Backup',
          'Automatically backup data to cloud storage',
          'cloud-upload'
        )}
        
        {renderSettingItem(
          'analytics',
          'Usage Analytics',
          'Help improve the app by sharing usage data',
          'chart-line'
        )}
      </Surface>

      {/* About Section */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
        <View style={styles.sectionHeader}>
          <Icon name="information" size={20} color={theme.colors.tertiary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>About</Text>
        </View>
        
        <View style={styles.aboutContent}>
          <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center' }}>
            Bakery Management App
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 4 }}>
            Version 1.0.0
          </Text>
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center', marginTop: 8 }}>
            Built with React Native & Material Design 3
          </Text>
        </View>
      </Surface>
    </CustomBottomSheet>
  );
});

const styles = StyleSheet.create({
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
    gap: 8,
  },
  sectionTitle: {
    fontWeight: '600',
  },
  settingItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    width: 36,
    height: 36,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontWeight: '500',
    marginBottom: 2,
  },
  settingSubtitle: {
    lineHeight: 16,
  },
  divider: {
    marginVertical: 4,
    opacity: 0.3,
  },
  aboutContent: {
    alignItems: 'center',
    paddingVertical: 8,
  },
  footerActions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

SettingsBottomSheet.displayName = 'SettingsBottomSheet';

export default SettingsBottomSheet;
