import React, { useState, useEffect } from 'react';
import { View, StyleSheet, ScrollView } from 'react-native';
import {
  Modal,
  Portal,
  Text,
  Button,
  useTheme,
  Card,
  List,
  Chip,
  Surface,
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import UnifiedSearch from './UnifiedSearch';

const ActivityLogModal = ({ visible, onDismiss, activities }) => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedType, setSelectedType] = useState('All');

  const activityTypes = ['All', 'Product', 'Order', 'Customer', 'Settings', 'System'];

  // No sample activities - start with empty state
  const sampleActivities = [];

  const activityData = activities || sampleActivities;

  const filteredActivities = activityData.filter(activity => {
    const matchesSearch = activity.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         activity.action.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesType = selectedType === 'All' || activity.type === selectedType;
    return matchesSearch && matchesType;
  });

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
    return `${Math.floor(diffInMinutes / 1440)}d ago`;
  };

  const ActivityItem = ({ activity }) => (
    <Card style={styles.activityCard} mode="outlined">
      <Card.Content>
        <View style={styles.activityHeader}>
          <View style={styles.activityInfo}>
            <Surface
              style={[
                styles.iconContainer,
                { backgroundColor: activity.color + '20' }
              ]}
              elevation={0}
            >
              <Icon name={activity.icon} size={20} color={activity.color} />
            </Surface>
            <View style={styles.activityText}>
              <View style={styles.activityTitleRow}>
                <Text variant="bodyMedium" style={{ fontWeight: 'bold' }}>
                  {activity.action}
                </Text>
                <Chip
                  mode="outlined"
                  compact
                  style={styles.typeChip}
                  textStyle={{ fontSize: 10 }}
                >
                  {activity.type}
                </Chip>
              </View>
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                {activity.description}
              </Text>
              <View style={styles.activityMeta}>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                  {formatTimestamp(activity.timestamp)}
                </Text>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                  • {activity.user}
                </Text>
              </View>
            </View>
          </View>
        </View>
      </Card.Content>
    </Card>
  );

  const getTypeStats = () => {
    const stats = {};
    activityTypes.slice(1).forEach(type => {
      stats[type] = activityData.filter(activity => activity.type === type).length;
    });
    return stats;
  };

  const typeStats = getTypeStats();

  return (
    <Portal>
      <Modal
        visible={visible}
        onDismiss={onDismiss}
        contentContainerStyle={[
          styles.modal,
          { backgroundColor: theme.colors.surface }
        ]}
      >
        <ScrollView showsVerticalScrollIndicator={false}>
          <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
            Activity Log
          </Text>

          <UnifiedSearch
            type="activities"
            mode="bar"
            data={activityData}
            searchFields={["description", "action", "type"]}
            placeholder="Search activities..."
            onSearch={setSearchQuery}
            onResultSelect={(activity) => {
              console.log('Activity selected from modal search:', activity);
            }}
            showSuggestions={true}
            showRecentSearches={false}
            showFilters={true}
            filters={activityTypes}
            onFilterChange={setSelectedType}
            style={styles.searchbar}
          />



          <Surface style={[styles.statsCard, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
            <Text variant="titleMedium" style={{ marginBottom: 8 }}>
              Activity Summary
            </Text>
            <Text variant="bodyMedium">
              {filteredActivities.length} activit{filteredActivities.length !== 1 ? 'ies' : 'y'} found
            </Text>
            {searchQuery && (
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginTop: 4 }}>
                Filtered by: "{searchQuery}"
              </Text>
            )}
          </Surface>

          {filteredActivities.length === 0 ? (
            <Card style={styles.emptyCard} mode="outlined">
              <Card.Content style={styles.emptyContent}>
                <Icon name="history" size={48} color={theme.colors.onSurfaceVariant} />
                <Text variant="titleMedium" style={{ marginTop: 16, textAlign: 'center' }}>
                  No Activities Found
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center' }}>
                  {searchQuery ? 'Try adjusting your search terms' : 'No activities to display'}
                </Text>
              </Card.Content>
            </Card>
          ) : (
            filteredActivities.map((activity) => (
              <ActivityItem key={activity.id} activity={activity} />
            ))
          )}

          <Button
            mode="outlined"
            onPress={onDismiss}
            style={styles.closeButton}
          >
            Close
          </Button>
        </ScrollView>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  modal: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    maxHeight: '90%',
  },
  title: {
    textAlign: 'center',
    marginBottom: 24,
  },
  searchbar: {
    marginBottom: 16,
  },
  filterContainer: {
    marginBottom: 16,
  },
  filterChip: {
    marginRight: 8,
  },
  statsCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },
  activityCard: {
    marginBottom: 8,
  },
  activityHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  activityInfo: {
    flexDirection: 'row',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  activityText: {
    flex: 1,
  },
  activityTitleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  typeChip: {
    height: 24,
  },
  activityMeta: {
    flexDirection: 'row',
    marginTop: 8,
  },
  emptyCard: {
    marginVertical: 32,
  },
  emptyContent: {
    alignItems: 'center',
    padding: 32,
  },
  closeButton: {
    marginTop: 24,
  },
});

export default ActivityLogModal;
