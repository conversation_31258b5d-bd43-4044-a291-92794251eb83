import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { Text, Surface, useTheme, Portal, Modal } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';

const QuickActionsWidget = ({ navigation, onAction }) => {
  const theme = useTheme();
  const { products, addOrder } = useData();
  const [showQuickOrderModal, setShowQuickOrderModal] = useState(false);

  const quickActions = [
    {
      id: 'new-order',
      title: 'New Order',
      icon: 'plus-circle',
      color: theme.colors.primary,
      action: () => navigation.navigate('Orders'),
    },
    {
      id: 'quick-sale',
      title: 'Quick Sale',
      icon: 'cash-fast',
      color: theme.colors.tertiary,
      action: () => setShowQuickOrderModal(true),
    },
    {
      id: 'add-product',
      title: 'Add Product',
      icon: 'package-variant',
      color: theme.colors.secondary,
      action: () => navigation.navigate('Products'),
    },
    {
      id: 'daily-report',
      title: 'Daily Report',
      icon: 'chart-line',
      color: '#9C27B0',
      action: () => navigation.navigate('Settings'),
    },
    {
      id: 'low-stock',
      title: 'Low Stock',
      icon: 'alert-circle',
      color: '#F44336',
      action: () => checkLowStock(),
    },
    {
      id: 'backup',
      title: 'Backup Data',
      icon: 'cloud-upload',
      color: '#2196F3',
      action: () => handleBackup(),
    },
  ];

  const checkLowStock = () => {
    const lowStockItems = products.filter(product => product.stock <= 5);

    if (lowStockItems.length === 0) {
      Alert.alert('Stock Status', 'All products are well stocked!');
    } else {
      const itemNames = lowStockItems.map(item => `• ${item.name} (${item.stock} left)`).join('\n');
      Alert.alert(
        'Low Stock Alert',
        `The following items are running low:\n\n${itemNames}`,
        [
          { text: 'OK', style: 'default' },
          { text: 'View Products', onPress: () => navigation.navigate('Products') },
        ]
      );
    }
  };

  const handleBackup = () => {
    Alert.alert(
      'Backup Data',
      'This will create a backup of all your data. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Backup',
          onPress: () => {
            // Implement backup logic
            Alert.alert('Success', 'Data backed up successfully!');
          },
        },
      ]
    );
  };

  const handleQuickSale = (productId) => {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const newOrder = {
      id: Date.now(),
      customer: 'Walk-in Customer',
      items: [{ ...product, quantity: 1 }],
      total: product.price,
      status: 'Completed',
      date: new Date().toLocaleDateString(),
      time: new Date().toLocaleTimeString(),
      paymentMethod: 'Cash',
    };

    addOrder(newOrder);
    setShowQuickOrderModal(false);
    Alert.alert('Quick Sale', `Sale of ${product.name} completed!`);
  };

  const renderQuickAction = (action) => (
    <TouchableOpacity
      key={action.id}
      style={[styles.actionButton, { backgroundColor: theme.colors.surface }]}
      onPress={() => {
        onAction?.(action.id);
        action.action();
      }}
      activeOpacity={0.7}
    >
      <Surface style={[styles.iconContainer, { backgroundColor: action.color + '20' }]} elevation={0}>
        <Icon name={action.icon} size={24} color={action.color} />
      </Surface>
      <Text variant="labelMedium" style={{ color: theme.colors.onSurface, textAlign: 'center' }}>
        {action.title}
      </Text>
    </TouchableOpacity>
  );

  const renderQuickOrderModal = () => (
    <Portal>
      <Modal
        visible={showQuickOrderModal}
        onDismiss={() => setShowQuickOrderModal(false)}
        contentContainerStyle={[styles.modalContainer, { backgroundColor: theme.colors.surface }]}
      >
        <Text variant="headlineSmall" style={{ color: theme.colors.onSurface, marginBottom: 16 }}>
          Quick Sale
        </Text>
        <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, marginBottom: 20 }}>
          Select a product for quick sale:
        </Text>

        <View style={styles.productGrid}>
          {products.slice(0, 6).map(product => (
            <TouchableOpacity
              key={product.id}
              style={[styles.productButton, { backgroundColor: theme.colors.surfaceVariant }]}
              onPress={() => handleQuickSale(product.id)}
            >
              <Text variant="labelMedium" style={{ color: theme.colors.onSurface }}>
                {product.name}
              </Text>
              <Text variant="bodySmall" style={{ color: theme.colors.primary }}>
                ${product.price.toFixed(2)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <TouchableOpacity
          style={[styles.cancelButton, { backgroundColor: theme.colors.surfaceVariant }]}
          onPress={() => setShowQuickOrderModal(false)}
        >
          <Text variant="labelLarge" style={{ color: theme.colors.onSurface }}>
            Cancel
          </Text>
        </TouchableOpacity>
      </Modal>
    </Portal>
  );

  return (
    <View style={styles.container}>
      <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginBottom: 12 }}>
        Quick Actions
      </Text>

      <View style={styles.actionsGrid}>
        {quickActions.map(renderQuickAction)}
      </View>

      {renderQuickOrderModal()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  actionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '30%',
    aspectRatio: 1,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    elevation: 2,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  modalContainer: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    maxHeight: '80%',
  },
  productGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 20,
  },
  productButton: {
    width: '48%',
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: 8,
  },
  cancelButton: {
    padding: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
});

export default QuickActionsWidget;
