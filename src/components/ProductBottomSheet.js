import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { View, StyleSheet, ScrollView, Keyboard } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  useTheme,
  HelperText,
  Surface,
  IconButton,
  Menu,
} from 'react-native-paper';

import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CustomBottomSheet from './CustomBottomSheet';

const ProductBottomSheet = ({ bottomSheetRef, onSave, product = null, mode = 'add' }) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const [formData, setFormData] = useState({
    name: '',
    category: 'Pastries',
    price: '',
    stock: '',
    description: '',
    icon: 'food-croissant',
  });
  const [errors, setErrors] = useState({});
  const [categoryMenuVisible, setCategoryMenuVisible] = useState(false);

  const snapPoints = useMemo(() => ['25%', '95%'], []);

  const categories = [
    { value: 'Bread', label: 'Bread' },
    { value: 'Pastries', label: 'Pastries' },
    { value: 'Cakes', label: 'Cakes' },
    { value: 'Beverages', label: 'Beverages' },
    { value: 'Cookies', label: 'Cookies' },
    { value: 'Muffins', label: 'Muffins' },
    { value: 'Sandwiches', label: 'Sandwiches' },
    { value: 'Desserts', label: 'Desserts' },
    { value: 'Hot Drinks', label: 'Hot Drinks' },
    { value: 'Cold Drinks', label: 'Cold Drinks' },
  ];

  const iconOptions = [
    { category: 'Bread', icons: ['bread-slice', 'bread-slice-outline', 'baguette'] },
    { category: 'Pastries', icons: ['food-croissant', 'pretzel', 'food-variant'] },
    { category: 'Cakes', icons: ['cake', 'cake-variant', 'cupcake'] },
    { category: 'Beverages', icons: ['coffee', 'tea', 'cup'] },
    { category: 'Cookies', icons: ['cookie', 'cookie-outline', 'food-variant'] },
    { category: 'Muffins', icons: ['muffin', 'cupcake', 'food-variant'] },
    { category: 'Sandwiches', icons: ['food', 'hamburger', 'food-variant'] },
    { category: 'Desserts', icons: ['ice-cream', 'candy', 'cake-variant'] },
    { category: 'Hot Drinks', icons: ['coffee', 'tea', 'cup-water'] },
    { category: 'Cold Drinks', icons: ['cup', 'bottle-soda', 'glass-mug'] },
  ];

  useEffect(() => {
    if (product && mode === 'edit') {
      setFormData({
        name: product.name,
        category: product.category,
        price: product.price.toString(),
        stock: product.stock.toString(),
        description: product.description,
        icon: product.icon,
      });
    } else {
      setFormData({
        name: '',
        category: 'Pastries',
        price: '',
        stock: '',
        description: '',
        icon: 'food-croissant',
      });
    }
    setErrors({});
  }, [product, mode]);

  const validateForm = () => {
    const newErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required';
    }

    if (!formData.price || isNaN(parseFloat(formData.price)) || parseFloat(formData.price) <= 0) {
      newErrors.price = 'Valid price is required';
    }

    if (!formData.stock || isNaN(parseInt(formData.stock)) || parseInt(formData.stock) < 0) {
      newErrors.stock = 'Valid stock quantity is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = () => {
    if (validateForm()) {
      const productData = {
        ...formData,
        price: parseFloat(formData.price),
        stock: parseInt(formData.stock),
        createdAt: product?.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      if (mode === 'edit') {
        productData.id = product.id;
      }

      onSave(productData);
      bottomSheetRef.current?.close();
    }
  };

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
    }
  }, []);

  const getCurrentIcons = () => {
    const categoryIcons = iconOptions.find(opt => opt.category === formData.category);
    return categoryIcons ? categoryIcons.icons : ['food-croissant'];
  };

  const footerButtons = (
    <View style={styles.buttonRow}>
      <Button
        mode="outlined"
        onPress={() => bottomSheetRef.current?.close()}
        style={styles.button}
      >
        Cancel
      </Button>
      <Button
        mode="contained"
        onPress={handleSave}
        style={styles.button}
      >
        {mode === 'edit' ? 'Update' : 'Add'} Product
      </Button>
    </View>
  );

  return (
    <CustomBottomSheet
      ref={bottomSheetRef}
      snapPoints={snapPoints}
      onChange={handleSheetChanges}
      title={mode === 'edit' ? 'Edit Product' : 'Add New Product'}
      icon="package-variant"
      footerContent={footerButtons}
      onClose={() => {
        setFormData({
          name: '',
          category: 'Pastries',
          price: '',
          stock: '',
          description: '',
          icon: 'food-croissant',
          image: null,
        });
        setErrors({});
      }}
    >
          <TextInput
            label="Product Name"
            value={formData.name}
            onChangeText={(text) => setFormData({ ...formData, name: text })}
            mode="outlined"
            style={styles.input}
            error={!!errors.name}
          />
          <HelperText type="error" visible={!!errors.name}>
            {errors.name}
          </HelperText>

          <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Category
          </Text>
          <Menu
            visible={categoryMenuVisible}
            onDismiss={() => setCategoryMenuVisible(false)}
            anchor={
              <Surface style={[styles.dropdownButton, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
                <Button
                  mode="text"
                  onPress={() => setCategoryMenuVisible(true)}
                  contentStyle={styles.dropdownContent}
                  labelStyle={{ color: theme.colors.onSurface }}
                  icon="chevron-down"
                  compact
                >
                  {formData.category}
                </Button>
              </Surface>
            }
          >
            {categories.map((category) => (
              <Menu.Item
                key={category.value}
                onPress={() => {
                  setFormData({
                    ...formData,
                    category: category.value,
                    icon: iconOptions.find(opt => opt.category === category.value)?.icons[0] || 'food-croissant'
                  });
                  setCategoryMenuVisible(false);
                }}
                title={category.label}
              />
            ))}
          </Menu>

          <View style={styles.row}>
            <TextInput
              label="Price ($)"
              value={formData.price}
              onChangeText={(text) => setFormData({ ...formData, price: text })}
              mode="outlined"
              style={[styles.input, styles.halfWidth]}
              keyboardType="decimal-pad"
              error={!!errors.price}
            />
            <TextInput
              label="Stock"
              value={formData.stock}
              onChangeText={(text) => setFormData({ ...formData, stock: text })}
              mode="outlined"
              style={[styles.input, styles.halfWidth]}
              keyboardType="number-pad"
              error={!!errors.stock}
            />
          </View>
          <View style={styles.row}>
            <HelperText type="error" visible={!!errors.price} style={styles.halfWidth}>
              {errors.price}
            </HelperText>
            <HelperText type="error" visible={!!errors.stock} style={styles.halfWidth}>
              {errors.stock}
            </HelperText>
          </View>

          <TextInput
            label="Description"
            value={formData.description}
            onChangeText={(text) => setFormData({ ...formData, description: text })}
            mode="outlined"
            style={styles.input}
            multiline
            numberOfLines={3}
            error={!!errors.description}
          />
          <HelperText type="error" visible={!!errors.description}>
            {errors.description}
          </HelperText>

          <Text variant="titleSmall" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Icon
          </Text>
          <View style={styles.iconGrid}>
            {getCurrentIcons().map((iconName) => (
              <Surface
                key={iconName}
                style={[
                  styles.iconOption,
                  {
                    backgroundColor: formData.icon === iconName
                      ? theme.colors.primaryContainer
                      : theme.colors.surfaceVariant
                  }
                ]}
                elevation={formData.icon === iconName ? 2 : 0}
              >
                <IconButton
                  icon={iconName}
                  size={24}
                  iconColor={formData.icon === iconName
                    ? theme.colors.onPrimaryContainer
                    : theme.colors.onSurfaceVariant
                  }
                  onPress={() => setFormData({ ...formData, icon: iconName })}
                />
              </Surface>
            ))}
          </View>

    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  input: {
    marginBottom: 4,
  },
  sectionTitle: {
    marginTop: 8,
    marginBottom: 6,
    fontWeight: '600',
  },
  dropdownButton: {
    borderRadius: 6,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: 'rgba(0,0,0,0.1)',
  },
  dropdownContent: {
    justifyContent: 'space-between',
    paddingHorizontal: 10,
    height: 36,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  halfWidth: {
    width: '48%',
  },
  iconGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  iconOption: {
    width: '30%',
    marginBottom: 6,
    borderRadius: 6,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    marginBottom: 16,
  },
  button: {
    flex: 1,
    marginHorizontal: 4,
  },
});

export default ProductBottomSheet;
