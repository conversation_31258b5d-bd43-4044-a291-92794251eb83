/**
 * Reusable BottomSheet Component
 *
 * @component
 * @description A comprehensive, reusable bottom sheet component that provides
 * consistent styling, behavior, and functionality across the entire app.
 *
 * @param {Object} props - Component props
 * @param {React.Ref} ref - Bottom sheet reference
 * @param {string[]} props.snapPoints - Array of snap points (e.g., ['25%', '80%'])
 * @param {number} [props.index=-1] - Initial index (-1 for closed)
 * @param {boolean} [props.enablePanDownToClose=true] - Enable pan down to close
 * @param {Function} [props.onChange] - Callback when sheet changes
 * @param {Function} [props.onClose] - Callback when sheet closes
 * @param {string} [props.title] - Header title
 * @param {string} [props.subtitle] - Header subtitle
 * @param {string} [props.icon] - Header icon name
 * @param {string} [props.iconColor] - Header icon color
 * @param {string} [props.iconBackground] - Header icon background color
 * @param {boolean} [props.showCloseButton=true] - Show close button in header
 * @param {boolean} [props.showHeader=true] - Show header section
 * @param {React.ReactNode} [props.headerLeft] - Custom left header content
 * @param {React.ReactNode} [props.headerRight] - Custom right header content
 * @param {React.ReactNode} [props.headerContent] - Custom header content (replaces default)
 * @param {React.ReactNode} [props.footerContent] - Footer content
 * @param {boolean} [props.scrollable=true] - Make content scrollable
 * @param {Object} [props.contentStyle] - Custom content styles
 * @param {Object} [props.headerStyle] - Custom header styles
 * @param {Object} [props.footerStyle] - Custom footer styles
 * @param {React.ReactNode} props.children - Sheet content
 *
 * @example
 * ```jsx
 * <CustomBottomSheet
 *   ref={bottomSheetRef}
 *   snapPoints={['25%', '80%']}
 *   title="Add Product"
 *   icon="plus"
 *   onClose={() => console.log('closed')}
 * >
 *   <Text>Content here</Text>
 * </CustomBottomSheet>
 * ```
 */

import React, { forwardRef, useCallback, useMemo } from 'react';
import { View, StyleSheet, Platform, Keyboard } from 'react-native';
import {
  Text,
  IconButton,
  useTheme,
  Surface,
  Divider
} from 'react-native-paper';
import BottomSheet, {
  BottomSheetBackdrop,
  BottomSheetView,
  BottomSheetScrollView
} from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Portal } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const CustomBottomSheet = forwardRef((props, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();

  const {
    snapPoints = ['50%'],
    index = -1,
    enablePanDownToClose = true,
    onChange,
    onClose,
    title,
    subtitle,
    icon,
    iconColor,
    iconBackground,
    showCloseButton = true,
    showHeader = true,
    headerLeft,
    headerRight,
    headerContent,
    footerContent,
    scrollable = true,
    contentStyle,
    headerStyle,
    footerStyle,
    children,
    ...bottomSheetProps
  } = props;

  // Enhanced backdrop component
  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  // Handle sheet changes
  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
      onClose?.();
    }
    onChange?.(index);
  }, [onChange, onClose]);

  // Handle close button press
  const handleClose = useCallback(() => {
    ref.current?.close();
  }, [ref]);

  // Render header content
  const renderHeader = useMemo(() => {
    if (!showHeader) return null;

    if (headerContent) {
      return (
        <View style={[styles.header, headerStyle]}>
          {headerContent}
        </View>
      );
    }

    return (
      <View style={[styles.header, headerStyle]}>
        <View style={styles.headerLeft}>
          {headerLeft || (
            <>
              {icon && (
                <View style={[
                  styles.iconContainer,
                  {
                    backgroundColor: iconBackground || (iconColor ? iconColor + '15' : theme.colors.primary + '15')
                  }
                ]}>
                  <Icon
                    name={icon}
                    size={24}
                    color={iconColor || theme.colors.primary}
                  />
                </View>
              )}
              <View style={styles.titleContainer}>
                {title && (
                  <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                    {title}
                  </Text>
                )}
                {subtitle && (
                  <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                    {subtitle}
                  </Text>
                )}
              </View>
            </>
          )}
        </View>

        <View style={styles.headerRight}>
          {headerRight || (showCloseButton && (
            <IconButton
              icon="close"
              size={20}
              onPress={handleClose}
              iconColor={theme.colors.onSurfaceVariant}
            />
          ))}
        </View>
      </View>
    );
  }, [
    showHeader,
    headerContent,
    headerStyle,
    headerLeft,
    icon,
    iconBackground,
    iconColor,
    theme,
    title,
    subtitle,
    headerRight,
    showCloseButton,
    handleClose
  ]);

  // Render content wrapper
  const ContentWrapper = scrollable ? BottomSheetScrollView : BottomSheetView;

  return (
    <Portal>
      <BottomSheet
        ref={ref}
        index={index}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={bottomSheetProps.backdropComponent || renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={enablePanDownToClose}
        animateOnMount={true}
        overDragResistanceFactor={0}
        enableContentPanningGesture={false}
        backgroundStyle={[
          { backgroundColor: theme.colors.surface },
          bottomSheetProps.backgroundStyle
        ]}
        handleIndicatorStyle={[
          { backgroundColor: theme.colors.onSurfaceVariant },
          bottomSheetProps.handleIndicatorStyle
        ]}
        style={[
          styles.bottomSheet,
          {
            zIndex: 99999,
            elevation: 99999,
          },
          bottomSheetProps.style
        ]}
        containerStyle={[
          styles.container,
          {
            zIndex: 99999,
            elevation: 99999,
          },
          bottomSheetProps.containerStyle
        ]}
        bottomInset={0}
        topInset={0}
        detached={false}
        {...bottomSheetProps}
      >
        <BottomSheetView style={styles.wrapper}>
          {renderHeader}

          {showHeader && <Divider style={styles.headerDivider} />}

          <ContentWrapper
            style={[styles.content, contentStyle]}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {children}

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 20, 40) }} />
          </ContentWrapper>

          {footerContent && (
            <>
              <Divider style={styles.footerDivider} />
              <View style={[styles.footer, footerStyle, { paddingBottom: insets.bottom }]}>
                {footerContent}
              </View>
            </>
          )}
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

CustomBottomSheet.displayName = 'CustomBottomSheet';

const styles = StyleSheet.create({
  bottomSheet: {
    zIndex: 99999,
    elevation: 99999,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: -4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  container: {
    zIndex: 99999,
    elevation: 99999,
  },
  wrapper: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 56,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  headerDivider: {
    marginHorizontal: 16,
    opacity: 0.5,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 8,
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    paddingBottom: 12,
  },
  footerDivider: {
    marginHorizontal: 16,
    opacity: 0.5,
  },
});

export default CustomBottomSheet;
