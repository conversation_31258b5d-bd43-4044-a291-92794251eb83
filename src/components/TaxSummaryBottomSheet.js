import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { View, StyleSheet, ScrollView, Alert } from 'react-native';
import {
  Text,
  Button,
  useTheme,
  Card,
  DataTable,
  Divider,
  Chip,
} from 'react-native-paper';
import CustomBottomSheet from './CustomBottomSheet';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { FINANCIAL_CONFIG } from '../config/constants';

const TaxSummaryBottomSheet = forwardRef(({ data }, ref) => {
  const theme = useTheme();
  const bottomSheetRef = React.useRef(null);

  useImperativeHandle(ref, () => ({
    expand: () => {
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const formatCurrency = (amount) => {
    return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}${amount.toFixed(FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES)}`;
  };

  const formatPercentage = (value) => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const getTaxStatus = (amount) => {
    if (amount === 0) {
      return { status: 'No Tax Due', color: '#4CAF50', icon: 'check-circle' };
    } else if (amount < 1000) {
      return { status: 'Low Tax Liability', color: '#FF9800', icon: 'alert-circle' };
    } else {
      return { status: 'High Tax Liability', color: '#F44336', icon: 'alert' };
    }
  };

  const exportTaxReport = () => {
    Alert.alert(
      'Export Tax Report',
      'This will generate a detailed tax report for your accountant.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Export',
          onPress: () => {
            // Export functionality would go here
            console.log('Export tax report');
            Alert.alert('Success', 'Tax report exported successfully!');
          },
        },
      ]
    );
  };

  const scheduleTaxReminder = () => {
    Alert.alert(
      'Tax Reminder',
      'Set up automatic reminders for tax filing deadlines?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Set Reminder',
          onPress: () => {
            // Reminder functionality would go here
            Alert.alert('Success', 'Tax reminders have been set up!');
          },
        },
      ]
    );
  };

  if (!data) return null;

  const taxStatus = getTaxStatus(data.totalTaxLiability);

  const footerButtons = (
    <View style={styles.actionContainer}>
      <Button
        mode="outlined"
        onPress={scheduleTaxReminder}
        icon="bell"
        style={styles.actionButton}
      >
        Set Reminders
      </Button>
      <Button
        mode="contained"
        onPress={exportTaxReport}
        icon="download"
        style={styles.actionButton}
      >
        Export Report
      </Button>
    </View>
  );

  return (
    <CustomBottomSheet
      ref={bottomSheetRef}
      snapPoints={['90%']}
      title="Tax Summary"
      subtitle={`${new Date(data.period.startDate).toLocaleDateString()} - ${new Date(data.period.endDate).toLocaleDateString()}`}
      icon="calculator"
      iconColor={theme.colors.primary}
      footerContent={footerButtons}
    >

        {/* Tax Status Card */}
        <Card style={[styles.statusCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.statusHeader}>
              <Icon name={taxStatus.icon} size={32} color={taxStatus.color} />
              <View style={styles.statusInfo}>
                <Text variant="titleLarge" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                  {formatCurrency(data.totalTaxLiability)}
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Total Tax Liability
                </Text>
              </View>
            </View>
            <Chip
              icon={taxStatus.icon}
              style={{
                backgroundColor: taxStatus.color + '20',
                alignSelf: 'flex-start',
                marginTop: 12,
              }}
              textStyle={{ color: taxStatus.color }}
            >
              {taxStatus.status}
            </Chip>
          </Card.Content>
        </Card>

        {/* Sales Tax Details */}
        <Card style={[styles.taxCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.taxHeader}>
              <Icon name="cash-register" size={24} color="#2196F3" />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Sales Tax
              </Text>
            </View>

            <DataTable>
              <DataTable.Row>
                <DataTable.Cell>Taxable Sales</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(data.salesTax.taxableAmount)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Tax Rate</DataTable.Cell>
                <DataTable.Cell numeric>{formatPercentage(data.salesTax.rate)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Tax Amount</DataTable.Cell>
                <DataTable.Cell numeric>
                  <Text style={{ color: '#2196F3', fontWeight: '600' }}>
                    {formatCurrency(data.salesTax.amount)}
                  </Text>
                </DataTable.Cell>
              </DataTable.Row>
            </DataTable>

            <View style={styles.taxNote}>
              <Icon name="information" size={16} color={theme.colors.onSurfaceVariant} />
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 4 }}>
                Sales tax is collected on all taxable sales and must be remitted to tax authorities
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Income Tax Details */}
        <Card style={[styles.taxCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <View style={styles.taxHeader}>
              <Icon name="chart-line" size={24} color="#4CAF50" />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Income Tax
              </Text>
            </View>

            <DataTable>
              <DataTable.Row>
                <DataTable.Cell>Taxable Income</DataTable.Cell>
                <DataTable.Cell numeric>{formatCurrency(data.incomeTax.taxableAmount)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Tax Rate</DataTable.Cell>
                <DataTable.Cell numeric>{formatPercentage(data.incomeTax.rate)}</DataTable.Cell>
              </DataTable.Row>
              <DataTable.Row>
                <DataTable.Cell>Tax Amount</DataTable.Cell>
                <DataTable.Cell numeric>
                  <Text style={{ color: '#4CAF50', fontWeight: '600' }}>
                    {formatCurrency(data.incomeTax.amount)}
                  </Text>
                </DataTable.Cell>
              </DataTable.Row>
            </DataTable>

            <View style={styles.taxNote}>
              <Icon name="information" size={16} color={theme.colors.onSurfaceVariant} />
              <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant, marginLeft: 4 }}>
                Income tax is calculated on net profit after deducting business expenses
              </Text>
            </View>
          </Card.Content>
        </Card>

        {/* Tax Calendar */}
        <Card style={[styles.calendarCard, { backgroundColor: theme.colors.surfaceVariant }]}>
          <Card.Content>
            <View style={styles.calendarHeader}>
              <Icon name="calendar-clock" size={24} color={theme.colors.primary} />
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                Important Tax Dates
              </Text>
            </View>

            <View style={styles.datesList}>
              <View style={styles.dateItem}>
                <View style={styles.dateInfo}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                    Quarterly Sales Tax
                  </Text>
                  <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                    Due: Last day of month following quarter
                  </Text>
                </View>
                <Chip size="small" style={{ backgroundColor: '#FF9800' + '20' }}>
                  <Text style={{ color: '#FF9800' }}>Quarterly</Text>
                </Chip>
              </View>

              <View style={styles.dateItem}>
                <View style={styles.dateInfo}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                    Annual Income Tax
                  </Text>
                  <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                    Due: March 15th (Corporate) / April 15th (Individual)
                  </Text>
                </View>
                <Chip size="small" style={{ backgroundColor: '#4CAF50' + '20' }}>
                  <Text style={{ color: '#4CAF50' }}>Annual</Text>
                </Chip>
              </View>

              <View style={styles.dateItem}>
                <View style={styles.dateInfo}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                    Estimated Tax Payments
                  </Text>
                  <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                    Due: 15th of Jan, Apr, Jun, Sep
                  </Text>
                </View>
                <Chip size="small" style={{ backgroundColor: '#2196F3' + '20' }}>
                  <Text style={{ color: '#2196F3' }}>Quarterly</Text>
                </Chip>
              </View>
            </View>
          </Card.Content>
        </Card>

        {/* Tax Summary Table */}
        <Card style={[styles.summaryCard, { backgroundColor: theme.colors.primaryContainer }]}>
          <Card.Content>
            <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, marginBottom: 16 }}>
              Tax Summary
            </Text>

            <View style={styles.summaryRow}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer }}>
                Sales Tax
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '600' }}>
                {formatCurrency(data.salesTax.amount)}
              </Text>
            </View>

            <View style={styles.summaryRow}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer }}>
                Income Tax
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '600' }}>
                {formatCurrency(data.incomeTax.amount)}
              </Text>
            </View>

            <Divider style={[styles.divider, { backgroundColor: theme.colors.onPrimaryContainer + '30' }]} />

            <View style={styles.summaryRow}>
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                Total Tax Liability
              </Text>
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                {formatCurrency(data.totalTaxLiability)}
              </Text>
            </View>
          </Card.Content>
        </Card>
    </CustomBottomSheet>
  );
});

const styles = StyleSheet.create({
  statusCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusInfo: {
    marginLeft: 16,
    flex: 1,
  },
  taxCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  taxHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  taxNote: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginTop: 12,
    padding: 8,
    backgroundColor: 'rgba(0,0,0,0.05)',
    borderRadius: 8,
  },
  calendarCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  calendarHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  datesList: {
    gap: 12,
  },
  dateItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dateInfo: {
    flex: 1,
  },
  summaryCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  divider: {
    marginVertical: 8,
  },
  actionContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default TaxSummaryBottomSheet;
