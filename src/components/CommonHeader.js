/**
 * CommonHeader - Universal header component with search, profile, and notifications
 * Features responsive design and consistent styling across all screens
 */

import React, { useState } from 'react';
import { View, StyleSheet, Dimensions, TouchableOpacity } from 'react-native';
import {
  Text,
  Searchbar,
  IconButton,
  Surface,
  useTheme,
  Badge,
  Avatar
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';
import UnifiedSearch from './UnifiedSearch';
import navigationService from '../services/NavigationService';

const { width } = Dimensions.get('window');

const CommonHeader = ({
  title = "Sweet Delights",
  subtitle,
  searchPlaceholder = "Search...",
  searchValue = "",
  onSearchChange,
  showSearch = true,
  showNotifications = true,
  showProfile = true,
  notificationCount = 3,
  onNotificationPress,
  onProfilePress,
  backgroundColor,
  elevation = 4,
  style,
  // New unified search props
  searchType = "global",
  searchData = [],
  searchFields = ["name", "title", "description"],
  onSearchResult,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { state } = useData();
  const [localSearchValue, setLocalSearchValue] = useState(searchValue);

  const handleSearchChange = (value) => {
    setLocalSearchValue(value);
    if (onSearchChange) {
      onSearchChange(value);
    }
  };

  const headerBackgroundColor = backgroundColor || theme.colors.surface;

  return (
    <Surface
      style={[
        styles.container,
        {
          backgroundColor: headerBackgroundColor,
          paddingTop: insets.top + 4,
          borderBottomColor: theme.colors.outline + '20'
        },
        style
      ]}
      elevation={elevation}
    >
      {/* Main Header Row */}
      <View style={styles.headerRow}>
        {/* Left Section - Title */}
        <View style={styles.leftSection}>
          <Text
            variant="headlineSmall"
            style={[styles.title, { color: theme.colors.onSurface }]}
            numberOfLines={1}
          >
            {title}
          </Text>
          {subtitle && (
            <Text
              variant="bodySmall"
              style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}
              numberOfLines={1}
            >
              {subtitle}
            </Text>
          )}
        </View>

        {/* Right Section - Actions */}
        <View style={styles.rightSection}>
          {showSearch && (
            <UnifiedSearch
              type={searchType}
              mode="icon"
              data={searchData}
              searchFields={searchFields}
              placeholder={searchPlaceholder}
              onSearch={onSearchChange}
              onResultSelect={onSearchResult}
              iconSize={24}
              iconColor={theme.colors.onSurface}
              style={[styles.iconButton, { backgroundColor: 'rgba(255, 255, 255, 0.2)' }]}
            />
          )}

          {showNotifications && (
            <View style={styles.iconContainer}>
              <IconButton
                icon="bell-outline"
                size={24}
                iconColor={theme.colors.onSurface}
                onPress={onNotificationPress || (() => console.log('Notifications pressed'))}
                style={[styles.iconButton, { backgroundColor: 'rgba(255, 255, 255, 0.2)' }]}
              />
              {notificationCount > 0 && (
                <Badge
                  size={18}
                  style={[
                    styles.badge,
                    { backgroundColor: theme.colors.error }
                  ]}
                >
                  {notificationCount > 99 ? '99+' : notificationCount}
                </Badge>
              )}
            </View>
          )}

          {showProfile && (
            <TouchableOpacity
              style={styles.profileContainer}
              onPress={onProfilePress || (() => {
                console.log('Profile pressed - navigating to MyProfile');
                try {
                  navigationService.navigate('MyProfile');
                } catch (error) {
                  console.error('Failed to navigate to MyProfile:', error);
                }
              })}
            >
              {state.settings?.profileImage ? (
                <Avatar.Image
                  size={32}
                  source={{ uri: state.settings.profileImage }}
                  style={styles.profileAvatar}
                />
              ) : (
                <Avatar.Text
                  size={32}
                  label={state.settings?.storeName?.split(' ').map(word => word[0]).join('').substring(0, 2) || 'SD'}
                  style={[styles.profileAvatar, { backgroundColor: theme.colors.primary }]}
                />
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>


    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingBottom: 8,
    borderBottomWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 0.5 },
    shadowOpacity: 0.03,
    shadowRadius: 1,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    marginHorizontal: 8,
    marginBottom: 6,
  },
  leftSection: {
    flex: 1,
    marginRight: 12,
  },
  title: {
    fontWeight: '700',
    letterSpacing: 0.3,
    fontSize: 18,
  },
  subtitle: {
    marginTop: 1,
    opacity: 0.8,
    fontSize: 12,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    position: 'relative',
  },
  iconButton: {
    margin: 0,
    marginHorizontal: 2,
    width: 36,
    height: 36,
    borderRadius: 18,
  },
  badge: {
    position: 'absolute',
    top: 4,
    right: 4,
    minWidth: 14,
    height: 14,
    borderRadius: 7,
    fontSize: 8,
    fontWeight: '600',
  },
  profileContainer: {
    position: 'relative',
    marginHorizontal: 2,
  },
  profileAvatar: {
    borderWidth: 1.5,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  profileButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    margin: 0,
  },
  searchRow: {
    paddingHorizontal: 12,
    marginHorizontal: 8,
  },
  searchbar: {
    borderRadius: 20,
    height: 40,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
  },
});

export default CommonHeader;
