/**
 * CommonHeader - Universal header component with search, profile, and notifications
 * Features responsive design and consistent styling across all screens
 */

import React, { useState } from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import {
  Text,
  Searchbar,
  IconButton,
  Surface,
  useTheme,
  Badge,
  Avatar
} from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useData } from '../context/DataContext';

const { width } = Dimensions.get('window');

const CommonHeader = ({
  title = "Sweet Delights",
  subtitle,
  searchPlaceholder = "Search...",
  searchValue = "",
  onSearchChange,
  showSearch = true,
  showNotifications = true,
  showProfile = true,
  notificationCount = 3,
  onNotificationPress,
  onProfilePress,
  backgroundColor,
  elevation = 4,
  style,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { state } = useData();
  const [localSearchValue, setLocalSearchValue] = useState(searchValue);

  const handleSearchChange = (value) => {
    setLocalSearchValue(value);
    if (onSearchChange) {
      onSearchChange(value);
    }
  };

  const headerBackgroundColor = backgroundColor || theme.colors.surface;

  return (
    <Surface
      style={[
        styles.container,
        {
          backgroundColor: headerBackgroundColor,
          paddingTop: insets.top + 8,
          borderBottomColor: theme.colors.outline + '20'
        },
        style
      ]}
      elevation={elevation}
    >
      {/* Main Header Row */}
      <View style={styles.headerRow}>
        {/* Left Section - Title */}
        <View style={styles.leftSection}>
          <Text
            variant="headlineSmall"
            style={[styles.title, { color: theme.colors.onSurface }]}
            numberOfLines={1}
          >
            {title}
          </Text>
          {subtitle && (
            <Text
              variant="bodySmall"
              style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}
              numberOfLines={1}
            >
              {subtitle}
            </Text>
          )}
        </View>

        {/* Right Section - Actions */}
        <View style={styles.rightSection}>
          {showSearch && (
            <IconButton
              icon="magnify"
              size={24}
              iconColor={theme.colors.onSurface}
              onPress={() => console.log('Search pressed - open search modal')}
              style={styles.iconButton}
            />
          )}

          {showNotifications && (
            <View style={styles.iconContainer}>
              <IconButton
                icon="bell-outline"
                size={24}
                iconColor={theme.colors.onSurface}
                onPress={onNotificationPress || (() => console.log('Notifications pressed'))}
                style={styles.iconButton}
              />
              {notificationCount > 0 && (
                <Badge
                  size={18}
                  style={[
                    styles.badge,
                    { backgroundColor: theme.colors.error }
                  ]}
                >
                  {notificationCount > 99 ? '99+' : notificationCount}
                </Badge>
              )}
            </View>
          )}

          {showProfile && (
            <View style={styles.profileContainer}>
              {state.settings?.profileImage ? (
                <Avatar.Image
                  size={36}
                  source={{ uri: state.settings.profileImage }}
                  style={styles.profileAvatar}
                />
              ) : (
                <Avatar.Text
                  size={36}
                  label={state.settings?.storeName?.split(' ').map(word => word[0]).join('').substring(0, 2) || 'SD'}
                  style={[styles.profileAvatar, { backgroundColor: theme.colors.primary }]}
                />
              )}
              <IconButton
                icon="account-circle-outline"
                size={36}
                iconColor="transparent"
                onPress={onProfilePress || (() => console.log('Profile pressed'))}
                style={styles.profileButton}
              />
            </View>
          )}
        </View>
      </View>

      {/* Search Bar Row */}
      {showSearch && (
        <View style={styles.searchRow}>
          <Searchbar
            placeholder={searchPlaceholder}
            onChangeText={handleSearchChange}
            value={localSearchValue}
            style={[
              styles.searchbar,
              { backgroundColor: theme.colors.surfaceVariant }
            ]}
            inputStyle={{
              color: theme.colors.onSurface,
              fontSize: 16,
              paddingLeft: 8,
              textAlignVertical: 'center'
            }}
            iconColor={theme.colors.onSurfaceVariant}
            placeholderTextColor={theme.colors.onSurfaceVariant}
            elevation={0}
          />
        </View>
      )}
    </Surface>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingBottom: 16,
    borderBottomWidth: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  leftSection: {
    flex: 1,
    marginRight: 16,
  },
  title: {
    fontWeight: '700',
    letterSpacing: 0.5,
  },
  subtitle: {
    marginTop: 2,
    opacity: 0.8,
  },
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    position: 'relative',
  },
  iconButton: {
    margin: 0,
    marginHorizontal: 4,
  },
  badge: {
    position: 'absolute',
    top: 6,
    right: 6,
    minWidth: 18,
    height: 18,
    borderRadius: 9,
    fontSize: 10,
    fontWeight: '600',
  },
  profileContainer: {
    position: 'relative',
    marginHorizontal: 4,
  },
  profileAvatar: {
    borderWidth: 2,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  profileButton: {
    position: 'absolute',
    top: 0,
    left: 0,
    margin: 0,
  },
  searchRow: {
    paddingHorizontal: 16,
  },
  searchbar: {
    borderRadius: 25,
    height: 48,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
});

export default CommonHeader;
