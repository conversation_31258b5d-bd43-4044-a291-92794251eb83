import React, { useMemo } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import {
  Text,
  Button,
  useTheme,
  Surface,
  Divider,
  Chip,
  IconButton,
} from 'react-native-paper';

import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CustomBottomSheet from './CustomBottomSheet';

const OrderDetailsBottomSheet = ({
  bottomSheetRef,
  order,
  onUpdateStatus,
  onEdit,
  onDelete
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const snapPoints = useMemo(() => ['25%', '85%'], []);

  if (!order) return null;

  const getStatusColor = (status) => {
    switch (status) {
      case 'Pending': return theme.colors.tertiary;
      case 'In Progress': return theme.colors.primary;
      case 'Completed': return theme.colors.secondary;
      case 'Cancelled': return theme.colors.error;
      default: return theme.colors.onSurfaceVariant;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'Pending': return 'clock-outline';
      case 'In Progress': return 'progress-clock';
      case 'Completed': return 'check-circle';
      case 'Cancelled': return 'cancel';
      default: return 'help-circle';
    }
  };

  const handleStatusUpdate = (newStatus) => {
    Alert.alert(
      'Update Order Status',
      `Change order status to ${newStatus}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Confirm',
          onPress: () => {
            onUpdateStatus(order.id, newStatus);
            bottomSheetRef.current?.close();
          }
        },
      ]
    );
  };

  const handleEdit = () => {
    onEdit(order);
    bottomSheetRef.current?.close();
  };

  const handleDelete = () => {
    Alert.alert(
      'Delete Order',
      'Are you sure you want to delete this order?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: () => {
            onDelete(order);
            bottomSheetRef.current?.close();
          }
        },
      ]
    );
  };

  const getActionButtons = () => {
    const buttons = [];

    // Status-specific actions
    if (order.status === 'Pending') {
      buttons.push(
        <Button
          key="cancel"
          mode="outlined"
          onPress={() => handleStatusUpdate('Cancelled')}
          icon="cancel"
          style={styles.actionButton}
        >
          Cancel
        </Button>
      );
      buttons.push(
        <Button
          key="start"
          mode="contained"
          onPress={() => handleStatusUpdate('In Progress')}
          icon="play"
          style={styles.actionButton}
        >
          Start
        </Button>
      );
    } else if (order.status === 'In Progress') {
      buttons.push(
        <Button
          key="complete"
          mode="contained"
          onPress={() => handleStatusUpdate('Completed')}
          icon="check"
          style={styles.actionButton}
        >
          Complete
        </Button>
      );
    } else if (order.status === 'Cancelled') {
      buttons.push(
        <Button
          key="reactivate"
          mode="contained"
          onPress={() => handleStatusUpdate('Pending')}
          icon="restore"
          style={styles.actionButton}
        >
          Reactivate
        </Button>
      );
    }

    // Always show edit button
    buttons.push(
      <Button
        key="edit"
        mode="outlined"
        onPress={handleEdit}
        icon="pencil"
        style={styles.actionButton}
      >
        Edit
      </Button>
    );

    return buttons;
  };

  const footerActions = (
    <View style={styles.actionButtons}>
      {getActionButtons()}
    </View>
  );

  return (
    <CustomBottomSheet
      ref={bottomSheetRef}
      snapPoints={snapPoints}
      title={`Order #${order.id}`}
      subtitle={`${order.status} • ${order.date} ${order.time}`}
      icon="clipboard-list"
      iconColor={getStatusColor(order.status)}
      footerContent={footerActions}
    >
          {/* Customer Information */}
          <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <View style={styles.sectionHeader}>
              <Icon name="account" size={20} color={theme.colors.primary} />
              <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Customer Information
              </Text>
            </View>
            <View style={styles.customerInfo}>
              <Text variant="bodyLarge" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                {order.customer}
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                📞 {order.phone}
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                ✉️ {order.email}
              </Text>
            </View>
          </Surface>

          {/* Order Details */}
          <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <View style={styles.sectionHeader}>
              <Icon name="calendar-clock" size={20} color={theme.colors.primary} />
              <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Order Details
              </Text>
            </View>
            <View style={styles.orderMeta}>
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Date:</Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                  {order.date}
                </Text>
              </View>
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Time:</Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                  {order.time}
                </Text>
              </View>
              <View style={styles.metaRow}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Total:</Text>
                <Text variant="titleMedium" style={{ color: theme.colors.primary, fontWeight: '700' }}>
                  ${order.total.toFixed(2)}
                </Text>
              </View>
            </View>
          </Surface>

          {/* Order Items */}
          <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
            <View style={styles.sectionHeader}>
              <Icon name="food" size={20} color={theme.colors.primary} />
              <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
                Items ({order.items?.length || 0})
              </Text>
            </View>
            {order.items?.map((item, index) => (
              <View key={index} style={styles.orderItem}>
                <View style={styles.itemInfo}>
                  <Text variant="bodyLarge" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                    {item.name}
                  </Text>
                  <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                    ${item.price.toFixed(2)} × {item.quantity}
                  </Text>
                </View>
                <Text variant="bodyLarge" style={{ color: theme.colors.primary, fontWeight: '600' }}>
                  ${(item.price * item.quantity).toFixed(2)}
                </Text>
              </View>
            ))}
          </Surface>
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  sectionTitle: {
    fontWeight: '600',
  },
  customerInfo: {
    gap: 4,
  },
  orderMeta: {
    gap: 8,
  },
  metaRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  orderItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  itemInfo: {
    flex: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default OrderDetailsBottomSheet;
