/**
 * Customer Details BottomSheet - Example using CustomBottomSheet
 * Shows customer information with edit and contact actions
 */

import React, { forwardRef, useState, useImperativeHandle } from 'react';
import { View, StyleSheet } from 'react-native';
import { 
  Text, 
  Button, 
  Chip, 
  Surface, 
  useTheme,
  Divider 
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import CustomBottomSheet from './CustomBottomSheet';

const CustomerDetailsBottomSheet = forwardRef((props, ref) => {
  const theme = useTheme();
  const [customer, setCustomer] = useState(null);

  useImperativeHandle(ref, () => ({
    open: (customerData) => {
      setCustomer(customerData);
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    }
  }));

  const bottomSheetRef = React.useRef(null);

  const handleCall = () => {
    console.log('Calling customer:', customer?.phone);
    // Implement call functionality
  };

  const handleEmail = () => {
    console.log('Emailing customer:', customer?.email);
    // Implement email functionality
  };

  const handleEdit = () => {
    console.log('Editing customer:', customer?.name);
    // Implement edit functionality
    bottomSheetRef.current?.close();
  };

  const footerActions = (
    <View style={styles.footerActions}>
      <Button 
        mode="outlined" 
        icon="phone" 
        onPress={handleCall}
        style={styles.actionButton}
      >
        Call
      </Button>
      <Button 
        mode="outlined" 
        icon="email" 
        onPress={handleEmail}
        style={styles.actionButton}
      >
        Email
      </Button>
      <Button 
        mode="contained" 
        icon="pencil" 
        onPress={handleEdit}
        style={styles.actionButton}
      >
        Edit
      </Button>
    </View>
  );

  if (!customer) return null;

  return (
    <CustomBottomSheet
      ref={bottomSheetRef}
      snapPoints={['40%', '70%']}
      title={customer.name}
      subtitle={`Customer since ${new Date(customer.createdAt).getFullYear()}`}
      icon="account"
      iconColor={theme.colors.primary}
      footerContent={footerActions}
      onClose={() => setCustomer(null)}
    >
      {/* Contact Information */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
        <View style={styles.sectionHeader}>
          <Icon name="card-account-details" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Contact Information</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Icon name="phone" size={16} color={theme.colors.onSurfaceVariant} />
          <Text variant="bodyMedium" style={styles.infoText}>{customer.phone}</Text>
        </View>
        
        <View style={styles.infoRow}>
          <Icon name="email" size={16} color={theme.colors.onSurfaceVariant} />
          <Text variant="bodyMedium" style={styles.infoText}>{customer.email}</Text>
        </View>
      </Surface>

      {/* Order History */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
        <View style={styles.sectionHeader}>
          <Icon name="history" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Order History</Text>
        </View>
        
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              {customer.totalOrders || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Total Orders
            </Text>
          </View>
          
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.secondary, fontWeight: '700' }}>
              ${customer.totalSpent || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Total Spent
            </Text>
          </View>
        </View>
      </Surface>

      {/* Customer Status */}
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
        <View style={styles.sectionHeader}>
          <Icon name="star" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Customer Status</Text>
        </View>
        
        <View style={styles.chipContainer}>
          <Chip 
            icon="crown" 
            mode="flat"
            style={[styles.chip, { backgroundColor: theme.colors.primaryContainer }]}
            textStyle={{ color: theme.colors.onPrimaryContainer }}
          >
            VIP Customer
          </Chip>
          <Chip 
            icon="check-circle" 
            mode="flat"
            style={[styles.chip, { backgroundColor: theme.colors.secondaryContainer }]}
            textStyle={{ color: theme.colors.onSecondaryContainer }}
          >
            Verified
          </Chip>
        </View>
      </Surface>
    </CustomBottomSheet>
  );
});

const styles = StyleSheet.create({
  section: {
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  sectionTitle: {
    fontWeight: '600',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 12,
  },
  infoText: {
    flex: 1,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  chipContainer: {
    flexDirection: 'row',
    gap: 8,
    flexWrap: 'wrap',
  },
  chip: {
    marginBottom: 4,
  },
  footerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
});

CustomerDetailsBottomSheet.displayName = 'CustomerDetailsBottomSheet';

export default CustomerDetailsBottomSheet;
