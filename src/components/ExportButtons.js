/**
 * ExportButtons - Reusable component for PDF and XLSX export functionality
 */

import React from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { Button } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';

const ExportButtons = ({ 
  onExportPDF, 
  onExportXLSX, 
  theme, 
  disabled = false,
  style,
  buttonStyle = 'horizontal' // 'horizontal' or 'vertical'
}) => {
  const handlePDFExport = async () => {
    try {
      if (onExportPDF) {
        await onExportPDF();
      } else {
        Alert.alert('Export PDF', 'PDF export functionality will be implemented soon.');
      }
    } catch (error) {
      Alert.alert('Export Error', 'Failed to export PDF. Please try again.');
    }
  };

  const handleXLSXExport = async () => {
    try {
      if (onExportXLSX) {
        await onExportXLSX();
      } else {
        Alert.alert('Export XLSX', 'XLSX export functionality will be implemented soon.');
      }
    } catch (error) {
      Alert.alert('Export Error', 'Failed to export XLSX. Please try again.');
    }
  };

  const containerStyle = buttonStyle === 'vertical' 
    ? styles.verticalContainer 
    : styles.horizontalContainer;

  return (
    <View style={[containerStyle, style]}>
      <Button
        mode="contained-tonal"
        onPress={handlePDFExport}
        disabled={disabled}
        icon={({ size, color }) => (
          <Icon name="file-pdf-box" size={size} color={color} />
        )}
        style={[
          buttonStyle === 'vertical' ? styles.verticalButton : styles.horizontalButton,
          { backgroundColor: theme.colors.errorContainer }
        ]}
        textColor={theme.colors.onErrorContainer}
        compact={buttonStyle === 'horizontal'}
      >
        Export PDF
      </Button>
      
      <Button
        mode="contained-tonal"
        onPress={handleXLSXExport}
        disabled={disabled}
        icon={({ size, color }) => (
          <Icon name="file-excel-box" size={size} color={color} />
        )}
        style={[
          buttonStyle === 'vertical' ? styles.verticalButton : styles.horizontalButton,
          { backgroundColor: theme.colors.tertiaryContainer }
        ]}
        textColor={theme.colors.onTertiaryContainer}
        compact={buttonStyle === 'horizontal'}
      >
        Export XLSX
      </Button>
    </View>
  );
};

const styles = StyleSheet.create({
  horizontalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  verticalContainer: {
    flexDirection: 'column',
    gap: 8,
  },
  horizontalButton: {
    flex: 1,
    height: 48,
    justifyContent: 'center',
    borderRadius: 12,
  },
  verticalButton: {
    height: 48,
    justifyContent: 'center',
    borderRadius: 12,
  },
});

export default ExportButtons;
