import React, { useMemo } from 'react';
import { View, StyleSheet } from 'react-native';
import {
  Text,
  useTheme,
  Surface,
  IconButton,
  Divider,
} from 'react-native-paper';

import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
// CustomBottomSheet removed - using direct BottomSheet implementation


const StatsDetailsBottomSheet = ({
  bottomSheetRef,
  statsType,
  data,
  title,
  icon,
  color
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const snapPoints = useMemo(() => ['25%', '80%'], []);

  if (!statsType || !data) return null;

  const renderSalesDetails = () => (
    <View>
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="chart-line" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Sales Breakdown
          </Text>
        </View>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              ${data.todaysSales?.toFixed(2) || '0.00'}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Today's Sales
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.secondary, fontWeight: '700' }}>
              ${data.weekSales?.toFixed(2) || '0.00'}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              This Week
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.tertiary, fontWeight: '700' }}>
              ${data.monthSales?.toFixed(2) || '0.00'}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              This Month
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              ${data.totalRevenue?.toFixed(2) || '0.00'}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Total Revenue
            </Text>
          </View>
        </View>
      </Surface>

      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="trending-up" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Performance Metrics
          </Text>
        </View>
        <View style={styles.metricsList}>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Average Order Value:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              ${data.avgOrderValue?.toFixed(2) || '0.00'}
            </Text>
          </View>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Orders Today:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {data.ordersToday || 0}
            </Text>
          </View>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Completion Rate:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {data.completionRate?.toFixed(1) || '0.0'}%
            </Text>
          </View>
        </View>
      </Surface>
    </View>
  );

  const renderOrdersDetails = () => (
    <View>
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="clipboard-list" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Order Status Breakdown
          </Text>
        </View>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              {data.pending || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Pending
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.secondary, fontWeight: '700' }}>
              {data.inProgress || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              In Progress
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.tertiary, fontWeight: '700' }}>
              {data.completed || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Completed
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.error, fontWeight: '700' }}>
              {data.cancelled || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Cancelled
            </Text>
          </View>
        </View>
      </Surface>

      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="clock-outline" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Recent Activity
          </Text>
        </View>
        <View style={styles.metricsList}>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Orders Today:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {data.ordersToday || 0}
            </Text>
          </View>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Orders This Week:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {data.ordersWeek || 0}
            </Text>
          </View>
          <View style={styles.metricRow}>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>Average Processing Time:</Text>
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
              {data.avgProcessingTime || 'N/A'}
            </Text>
          </View>
        </View>
      </Surface>
    </View>
  );

  const renderProductsDetails = () => (
    <View>
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="food-croissant" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Product Categories
          </Text>
        </View>
        <View style={styles.metricsList}>
          {data.categories?.map((category, index) => (
            <View key={index} style={styles.metricRow}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                {category.name}:
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                {category.count} items
              </Text>
            </View>
          )) || (
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              No category data available
            </Text>
          )}
        </View>
      </Surface>

      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="star" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Top Performers
          </Text>
        </View>
        <View style={styles.metricsList}>
          {data.topProducts?.slice(0, 5).map((product, index) => (
            <View key={index} style={styles.metricRow}>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                {product.name}:
              </Text>
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, fontWeight: '600' }}>
                {product.sales || 0} sold
              </Text>
            </View>
          )) || (
            <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
              No sales data available
            </Text>
          )}
        </View>
      </Surface>
    </View>
  );

  const renderCustomersDetails = () => (
    <View>
      <Surface style={[styles.section, { backgroundColor: theme.colors.surfaceVariant }]} elevation={0}>
        <View style={styles.sectionHeader}>
          <Icon name="account-group" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={[styles.sectionTitle, { color: theme.colors.onSurface }]}>
            Customer Insights
          </Text>
        </View>
        <View style={styles.statsGrid}>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              {data.newCustomers || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              New This Month
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.secondary, fontWeight: '700' }}>
              {data.returningCustomers || 0}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Returning
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.tertiary, fontWeight: '700' }}>
              ${data.avgCustomerValue?.toFixed(2) || '0.00'}
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Avg. Value
            </Text>
          </View>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              {data.loyaltyRate?.toFixed(1) || '0.0'}%
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Loyalty Rate
            </Text>
          </View>
        </View>
      </Surface>
    </View>
  );

  const renderContent = () => {
    switch (statsType) {
      case 'sales':
        return renderSalesDetails();
      case 'orders':
        return renderOrdersDetails();
      case 'products':
        return renderProductsDetails();
      case 'customers':
        return renderCustomersDetails();
      default:
        return <Text>No data available</Text>;
    }
  };

  return (
    <CustomBottomSheet
      ref={bottomSheetRef}
      snapPoints={snapPoints}
      title={title}
      icon={icon}
      iconColor={color}
    >
      {renderContent()}
    </CustomBottomSheet>
  );
};

const styles = StyleSheet.create({
  section: {
    borderRadius: 10,
    padding: 12,
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 6,
  },
  sectionTitle: {
    fontWeight: '600',
  },
  statsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statItem: {
    width: '48%',
    alignItems: 'center',
    marginBottom: 8,
  },
  metricsList: {
    gap: 6,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
});

export default StatsDetailsBottomSheet;
