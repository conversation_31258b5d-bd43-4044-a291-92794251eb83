/**
 * CashReconciliationBottomSheet - Unified Cash Reconciliation
 * Redesigned with unified design inspired by QuickActionsBottomSheet
 */

import React, { useState, useCallback, forwardRef, useImperativeHandle, useEffect, useMemo } from 'react';
import { View, StyleSheet, Alert, ScrollView, Keyboard } from 'react-native';
import {
  Text,
  TextInput,
  Button,
  useTheme,
  Card,
  Divider,
  Chip,
  IconButton,
  Portal,
} from 'react-native-paper';
import BottomSheet, { BottomSheetView, BottomSheetBackdrop } from '@gorhom/bottom-sheet';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useFinancial } from '../context/FinancialContext';
import { FINANCIAL_CONFIG } from '../config/constants';

const CashReconciliationBottomSheet = forwardRef((props, ref) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const { performReconciliation, calculateDailyCashExpected } = useFinancial();
  const bottomSheetRef = React.useRef(null);

  const snapPoints = useMemo(() => ['90%'], []);
  const [reconciliation, setReconciliation] = useState({
    date: new Date().toISOString().split('T')[0],
    actualCash: '',
    notes: '',
    performedBy: 'Manager',
  });
  const [expectedCashData, setExpectedCashData] = useState(null);
  const [loading, setLoading] = useState(false);
  const [calculatingExpected, setCalculatingExpected] = useState(false);

  const handleSheetChanges = useCallback((index) => {
    if (index === -1) {
      Keyboard.dismiss();
      setReconciliation({
        date: new Date().toISOString().split('T')[0],
        actualCash: '',
        notes: '',
        performedBy: 'Manager',
      });
    }
  }, []);

  const renderBackdrop = useCallback(
    (props) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
        pressBehavior="close"
      />
    ),
    []
  );

  useImperativeHandle(ref, () => ({
    expand: () => {
      setReconciliation({
        date: new Date().toISOString().split('T')[0],
        actualCash: '',
        notes: '',
        performedBy: 'Manager',
      });
      bottomSheetRef.current?.expand();
      loadExpectedCash();
    },
    close: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const loadExpectedCash = useCallback(async () => {
    setCalculatingExpected(true);
    try {
      const data = await calculateDailyCashExpected(reconciliation.date);
      setExpectedCashData(data);
    } catch (error) {
      console.error('Failed to calculate expected cash:', error);
      Alert.alert('Warning', 'Could not calculate expected cash amount');
    } finally {
      setCalculatingExpected(false);
    }
  }, [reconciliation.date, calculateDailyCashExpected]);

  useEffect(() => {
    if (isVisible) {
      loadExpectedCash();
    }
  }, [reconciliation.date, isVisible, loadExpectedCash]);

  const formatCurrency = (amount) => {
    return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}${amount.toFixed(FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES)}`;
  };

  const calculateDifference = () => {
    const actual = parseFloat(reconciliation.actualCash) || 0;
    const expected = expectedCashData?.expectedClosingCash || 0;
    return actual - expected;
  };

  const getDifferenceStatus = () => {
    const difference = Math.abs(calculateDifference());
    if (difference <= FINANCIAL_CONFIG.RECONCILIATION_TOLERANCE) {
      return { status: 'balanced', color: '#4CAF50', icon: 'check-circle' };
    } else {
      return { status: 'discrepancy', color: '#F44336', icon: 'alert-circle' };
    }
  };

  const validateReconciliation = () => {
    if (!reconciliation.actualCash) {
      Alert.alert('Validation Error', 'Please enter the actual cash amount');
      return false;
    }

    const amount = parseFloat(reconciliation.actualCash);
    if (isNaN(amount) || amount < 0) {
      Alert.alert('Validation Error', 'Please enter a valid cash amount');
      return false;
    }

    return true;
  };

  const handlePerformReconciliation = useCallback(async () => {
    if (!validateReconciliation()) {
      return;
    }

    setLoading(true);
    try {
      const reconciliationData = {
        ...reconciliation,
        actualCash: parseFloat(reconciliation.actualCash),
        expectedCash: expectedCashData?.expectedClosingCash || 0,
      };

      const result = await performReconciliation(reconciliationData);

      const difference = Math.abs(result.difference);
      const isBalanced = difference <= FINANCIAL_CONFIG.RECONCILIATION_TOLERANCE;

      Alert.alert(
        'Reconciliation Complete',
        isBalanced
          ? `Cash reconciliation successful! Difference: ${formatCurrency(difference)}`
          : `Cash discrepancy detected! Difference: ${formatCurrency(difference)}. Please investigate.`,
        [{ text: 'OK', onPress: () => setIsVisible(false) }]
      );
    } catch (error) {
      Alert.alert('Error', error.message || 'Failed to perform reconciliation');
    } finally {
      setLoading(false);
    }
  }, [reconciliation, expectedCashData, performReconciliation]);

  const updateField = useCallback((field, value) => {
    setReconciliation(prev => ({ ...prev, [field]: value }));
  }, []);

  const differenceStatus = getDifferenceStatus();

  const getStatusInfo = () => {
    if (!reconciliation.actualCash || !expectedCashData) {
      return 'Enter actual cash amount';
    }
    const diff = Math.abs(calculateDifference());
    return `${differenceStatus.status === 'balanced' ? 'Balanced' : 'Discrepancy'} • ${formatCurrency(diff)}`;
  };

  return (
    <Portal>
      <BottomSheet
        ref={bottomSheetRef}
        index={-1}
        snapPoints={snapPoints}
        onChange={handleSheetChanges}
        backdropComponent={renderBackdrop}
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
        android_keyboardInputMode="adjustResize"
        enablePanDownToClose={true}
        backgroundStyle={{ backgroundColor: theme.colors.surface }}
        handleIndicatorStyle={{ backgroundColor: theme.colors.onSurfaceVariant }}
        style={styles.bottomSheet}
      >
        <BottomSheetView style={styles.container}>
          {/* Header - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.header, { borderBottomColor: theme.colors.outline + '20' }]}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: theme.colors.tertiary + '15' }]}>
                <Icon name="cash-register" size={24} color={theme.colors.tertiary} />
              </View>
              <View style={styles.titleContainer}>
                <Text variant="headlineSmall" style={[styles.title, { color: theme.colors.onSurface }]}>
                  Cash Reconciliation
                </Text>
                <Text variant="bodyMedium" style={[styles.subtitle, { color: theme.colors.onSurfaceVariant }]}>
                  {new Date(reconciliation.date).toLocaleDateString()} • {getStatusInfo()}
                </Text>
              </View>
            </View>
            <IconButton
              icon="close"
              size={20}
              onPress={() => bottomSheetRef.current?.close()}
              iconColor={theme.colors.onSurfaceVariant}
            />
          </View>

          {/* Content */}
          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Expected Cash Breakdown */}
            <Card style={[styles.expectedCard, { backgroundColor: theme.colors.surfaceVariant }]}>
              <Card.Content>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginBottom: 16 }}>
              Expected Cash Calculation
            </Text>

            {calculatingExpected ? (
              <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                Calculating expected cash...
              </Text>
            ) : expectedCashData ? (
              <View style={styles.expectedBreakdown}>
                <View style={styles.breakdownRow}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                    Opening Cash
                  </Text>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                    {formatCurrency(expectedCashData.openingCash)}
                  </Text>
                </View>

                <View style={styles.breakdownRow}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                    Cash Sales ({expectedCashData.orderCount} orders)
                  </Text>
                  <Text variant="bodyMedium" style={{ color: '#4CAF50' }}>
                    +{formatCurrency(expectedCashData.cashSales)}
                  </Text>
                </View>

                <View style={styles.breakdownRow}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                    Cash Expenses
                  </Text>
                  <Text variant="bodyMedium" style={{ color: '#F44336' }}>
                    -{formatCurrency(expectedCashData.cashExpenses)}
                  </Text>
                </View>

                <Divider style={styles.divider} />

                <View style={styles.breakdownRow}>
                  <Text variant="titleMedium" style={{ color: theme.colors.onSurface, fontWeight: '700' }}>
                    Expected Closing Cash
                  </Text>
                  <Text variant="titleMedium" style={{ color: theme.colors.primary, fontWeight: '700' }}>
                    {formatCurrency(expectedCashData.expectedClosingCash)}
                  </Text>
                </View>
              </View>
            ) : (
              <Text variant="bodyMedium" style={{ color: theme.colors.error }}>
                Could not calculate expected cash
              </Text>
            )}
          </Card.Content>
        </Card>

        {/* Actual Cash Input */}
        <View style={styles.form}>
          <TextInput
            label="Actual Cash Count *"
            value={reconciliation.actualCash}
            onChangeText={(text) => updateField('actualCash', text)}
            style={styles.input}
            mode="outlined"
            keyboardType="numeric"
            placeholder="0.00"
            left={<TextInput.Icon icon={() => (
              <Text style={{ color: theme.colors.onSurfaceVariant }}>
                {FINANCIAL_CONFIG.CURRENCY.SYMBOL}
              </Text>
            )} />}
          />

          {/* Difference Display */}
          {reconciliation.actualCash && expectedCashData && (
            <Card style={[styles.differenceCard, { backgroundColor: theme.colors.surface }]}>
              <Card.Content>
                <View style={styles.differenceHeader}>
                  <Icon
                    name={differenceStatus.icon}
                    size={24}
                    color={differenceStatus.color}
                  />
                  <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                    Reconciliation Status
                  </Text>
                </View>

                <View style={styles.differenceRow}>
                  <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                    Difference
                  </Text>
                  <Text variant="titleMedium" style={{
                    color: differenceStatus.color,
                    fontWeight: '700'
                  }}>
                    {calculateDifference() >= 0 ? '+' : ''}{formatCurrency(calculateDifference())}
                  </Text>
                </View>

                <Chip
                  icon={differenceStatus.icon}
                  style={{
                    backgroundColor: differenceStatus.color + '20',
                    alignSelf: 'flex-start',
                    marginTop: 8,
                  }}
                  textStyle={{ color: differenceStatus.color }}
                >
                  {differenceStatus.status === 'balanced' ? 'Balanced' : 'Discrepancy Detected'}
                </Chip>
              </Card.Content>
            </Card>
          )}

          {/* Performed By */}
          <TextInput
            label="Performed By"
            value={reconciliation.performedBy}
            onChangeText={(text) => updateField('performedBy', text)}
            style={styles.input}
            mode="outlined"
            placeholder="Manager name"
          />

          {/* Notes */}
          <TextInput
            label="Notes (Optional)"
            value={reconciliation.notes}
            onChangeText={(text) => updateField('notes', text)}
            style={styles.input}
            mode="outlined"
            multiline
            numberOfLines={3}
            placeholder="Any discrepancies or notes about the reconciliation..."
              />
            </View>

            {/* Bottom padding for safe area */}
            <View style={{ height: Math.max(insets.bottom + 80, 100) }} />
          </ScrollView>

          {/* Footer - Inspired by QuickActionsBottomSheet */}
          <View style={[styles.footer, { paddingBottom: insets.bottom, borderTopColor: theme.colors.outline + '20' }]}>
            <View style={styles.buttonRow}>
              <Button
                mode="outlined"
                onPress={() => bottomSheetRef.current?.close()}
                style={styles.button}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                mode="contained"
                onPress={handlePerformReconciliation}
                style={styles.button}
                loading={loading}
                disabled={loading || !expectedCashData}
              >
                Complete Reconciliation
              </Button>
            </View>
          </View>
        </BottomSheetView>
      </BottomSheet>
    </Portal>
  );
});

const styles = StyleSheet.create({
  bottomSheet: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 8,
  },
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  titleContainer: {
    flex: 1,
  },
  title: {
    fontWeight: '700',
    lineHeight: 28,
  },
  subtitle: {
    marginTop: 2,
    lineHeight: 20,
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
    paddingTop: 16,
  },
  expectedCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  expectedBreakdown: {
    gap: 8,
  },
  breakdownRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  divider: {
    marginVertical: 8,
  },
  form: {
    paddingBottom: 32,
  },
  input: {
    marginBottom: 16,
  },
  differenceCard: {
    marginBottom: 16,
    borderRadius: 12,
  },
  differenceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  differenceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  footer: {
    paddingHorizontal: 16,
    paddingTop: 12,
    borderTopWidth: 1,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  button: {
    flex: 1,
  },
});

export default CashReconciliationBottomSheet;
