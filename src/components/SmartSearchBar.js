import React, { useState, useCallback, useEffect, useRef } from 'react';
import { View, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Searchbar, Text, Surface, Chip, useTheme } from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { StorageService } from '../services/storageService';


const SmartSearchBar = ({
  data = [],
  onSearch,
  onResultSelect,
  placeholder = 'Search...',
  searchFields = ['name'],
  showSuggestions = true,
  showRecentSearches = true,
  maxSuggestions = 5,
  maxRecentSearches = 5,
}) => {
  const theme = useTheme();
  const insets = useSafeAreaInsets();
  const [query, setQuery] = useState('');
  const [suggestions, setSuggestions] = useState([]);
  const [recentSearches, setRecentSearches] = useState([]);
  const [showDropdown, setShowDropdown] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const searchTimeoutRef = useRef(null);

  // Load recent searches on mount
  useEffect(() => {
    loadRecentSearches();
  }, []);

  const loadRecentSearches = async () => {
    try {
      const recent = await StorageService.get('recentSearches', true) || [];
      setRecentSearches(recent.slice(0, maxRecentSearches));
    } catch (error) {
      console.warn('Failed to load recent searches:', error);
    }
  };

  const saveRecentSearch = async (searchQuery) => {
    if (!searchQuery.trim() || !showRecentSearches) return;

    try {
      const recent = await StorageService.get('recentSearches', true) || [];
      const updated = [
        searchQuery,
        ...recent.filter(item => item !== searchQuery)
      ].slice(0, maxRecentSearches);

      await StorageService.set('recentSearches', updated);
      setRecentSearches(updated);
    } catch (error) {
      console.warn('Failed to save recent search:', error);
    }
  };

  const clearRecentSearches = async () => {
    try {
      await StorageService.remove('recentSearches');
      setRecentSearches([]);
    } catch (error) {
      console.warn('Failed to clear recent searches:', error);
    }
  };

  const generateSuggestions = useCallback((searchQuery) => {
    if (!searchQuery.trim() || !showSuggestions) {
      setSuggestions([]);
      return;
    }

    const query = searchQuery.toLowerCase();
    const matches = data
      .filter(item => {
        return searchFields.some(field => {
          const value = item[field];
          return value && value.toString().toLowerCase().includes(query);
        });
      })
      .slice(0, maxSuggestions)
      .map(item => ({
        ...item,
        matchedField: searchFields.find(field =>
          item[field] && item[field].toString().toLowerCase().includes(query)
        )
      }));

    setSuggestions(matches);
  }, [data, searchFields, maxSuggestions, showSuggestions]);

  const handleQueryChange = (searchQuery) => {
    setQuery(searchQuery);
    setShowDropdown(true);

    // Debounce suggestions generation
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      generateSuggestions(searchQuery);
    }, 300);
  };

  const handleSearch = async (searchQuery = query) => {
    if (!searchQuery.trim()) return;

    setIsLoading(true);
    setShowDropdown(false);

    try {
      await saveRecentSearch(searchQuery);
      onSearch?.(searchQuery);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestionSelect = (item) => {
    setQuery(item.name || item[searchFields[0]] || '');
    setShowDropdown(false);
    onResultSelect?.(item);
    saveRecentSearch(item.name || item[searchFields[0]] || '');
  };

  const handleRecentSearchSelect = (searchQuery) => {
    setQuery(searchQuery);
    handleSearch(searchQuery);
  };

  const renderSuggestionItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.suggestionItem, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleSuggestionSelect(item)}
    >
      <Icon
        name="magnify"
        size={20}
        color={theme.colors.onSurfaceVariant}
        style={styles.suggestionIcon}
      />
      <View style={styles.suggestionContent}>
        <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
          {item.name || item[searchFields[0]]}
        </Text>
        {item.category && (
          <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
            in {item.category}
          </Text>
        )}
      </View>
      <Icon
        name="arrow-top-left"
        size={16}
        color={theme.colors.onSurfaceVariant}
      />
    </TouchableOpacity>
  );

  const renderRecentSearchItem = ({ item }) => (
    <TouchableOpacity
      style={[styles.suggestionItem, { backgroundColor: theme.colors.surface }]}
      onPress={() => handleRecentSearchSelect(item)}
    >
      <Icon
        name="history"
        size={20}
        color={theme.colors.onSurfaceVariant}
        style={styles.suggestionIcon}
      />
      <Text variant="bodyMedium" style={{ color: theme.colors.onSurface, flex: 1 }}>
        {item}
      </Text>
      <Icon
        name="arrow-top-left"
        size={16}
        color={theme.colors.onSurfaceVariant}
      />
    </TouchableOpacity>
  );

  const showRecentSearchesSection = showRecentSearches && recentSearches.length > 0 && !query.trim();
  const showSuggestionsSection = showSuggestions && suggestions.length > 0 && query.trim();

  return (
    <View style={[styles.container, { paddingTop: insets.top }]}>
      <Searchbar
        placeholder={placeholder}
        onChangeText={handleQueryChange}
        value={query}
        onSubmitEditing={() => handleSearch()}
        loading={isLoading}
        style={[styles.searchBar, { backgroundColor: theme.colors.surfaceVariant }]}
        inputStyle={{ color: theme.colors.onSurface }}
        iconColor={theme.colors.onSurfaceVariant}
        onFocus={() => setShowDropdown(true)}
        onBlur={() => setTimeout(() => setShowDropdown(false), 200)}
      />

      {showDropdown && (showRecentSearchesSection || showSuggestionsSection) && (
        <Surface style={[styles.dropdown, { backgroundColor: theme.colors.surface }]} elevation={4}>
          {showRecentSearchesSection && (
            <View>
              <View style={styles.sectionHeader}>
                <Text variant="labelMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Recent Searches
                </Text>
                <TouchableOpacity onPress={clearRecentSearches}>
                  <Text variant="labelSmall" style={{ color: theme.colors.primary }}>
                    Clear
                  </Text>
                </TouchableOpacity>
              </View>
              <FlatList
                data={recentSearches}
                renderItem={renderRecentSearchItem}
                keyExtractor={(item, index) => `recent-${index}`}
                scrollEnabled={false}
              />
            </View>
          )}

          {showSuggestionsSection && (
            <View>
              {showRecentSearchesSection && <View style={styles.separator} />}
              <View style={styles.sectionHeader}>
                <Text variant="labelMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  Suggestions
                </Text>
              </View>
              <FlatList
                data={suggestions}
                renderItem={renderSuggestionItem}
                keyExtractor={(item) => item.id?.toString() || item.name}
                scrollEnabled={false}
              />
            </View>
          )}
        </Surface>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000,
  },
  searchBar: {
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 25,
    elevation: 2,
  },
  dropdown: {
    position: 'absolute',
    top: 60,
    left: 16,
    right: 16,
    borderRadius: 12,
    maxHeight: 300,
    zIndex: 1001,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  suggestionIcon: {
    marginRight: 12,
  },
  suggestionContent: {
    flex: 1,
  },
  separator: {
    height: 1,
    backgroundColor: 'rgba(0,0,0,0.1)',
    marginVertical: 8,
  },
});

export default SmartSearchBar;
