/**
 * Quick Actions BottomSheet - Example using CustomBottomSheet
 * Shows common actions that can be performed quickly
 */

import React, { forwardRef } from 'react';
import { View, StyleSheet } from 'react-native';
import {
  Text,
  Surface,
  useTheme,
  TouchableRipple
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import CustomBottomSheet from './CustomBottomSheet';

const QuickActionsBottomSheet = forwardRef(({ onActionPress }, ref) => {
  const theme = useTheme();
  const bottomSheetRef = React.useRef(null);

  React.useImperativeHandle(ref, () => ({
    open: () => {
      console.log('⚡ Opening Quick Actions bottomsheet');
      bottomSheetRef.current?.expand();
    },
    close: () => {
      console.log('⚡ Closing Quick Actions bottomsheet');
      bottomSheetRef.current?.close();
    }
  }));

  const actions = [
    {
      id: 'add-product',
      title: 'Add Product',
      subtitle: 'Create a new bakery item',
      icon: 'plus-circle',
      color: theme.colors.primary,
      backgroundColor: theme.colors.primaryContainer,
    },
    {
      id: 'add-order',
      title: 'New Order',
      subtitle: 'Create customer order',
      icon: 'clipboard-plus',
      color: theme.colors.secondary,
      backgroundColor: theme.colors.secondaryContainer,
    },
    {
      id: 'add-customer',
      title: 'Add Customer',
      subtitle: 'Register new customer',
      icon: 'account-plus',
      color: theme.colors.tertiary,
      backgroundColor: theme.colors.tertiaryContainer,
    },
    {
      id: 'inventory',
      title: 'Check Inventory',
      subtitle: 'View stock levels',
      icon: 'package-variant',
      color: '#FF6B35',
      backgroundColor: '#FF6B3515',
    },
    {
      id: 'reports',
      title: 'View Reports',
      subtitle: 'Sales and analytics',
      icon: 'chart-line',
      color: '#8B5CF6',
      backgroundColor: '#8B5CF615',
    },
    {
      id: 'settings',
      title: 'Settings',
      subtitle: 'App preferences',
      icon: 'cog',
      color: '#6B7280',
      backgroundColor: '#6B728015',
    },
  ];

  const handleActionPress = (actionId) => {
    console.log(`⚡ Quick Action pressed: ${actionId}`);
    bottomSheetRef.current?.close();
    onActionPress?.(actionId);
  };

  const renderActionItem = (action) => (
    <Surface
      key={action.id}
      style={[styles.actionItem, { backgroundColor: theme.colors.surface }]}
      elevation={1}
    >
      <TouchableRipple
        onPress={() => handleActionPress(action.id)}
        style={styles.actionContent}
        borderless
      >
        <View style={styles.actionInner}>
          <View style={[styles.iconContainer, { backgroundColor: action.backgroundColor }]}>
            <Icon name={action.icon} size={24} color={action.color} />
          </View>

          <View style={styles.actionText}>
            <Text variant="titleMedium" style={[styles.actionTitle, { color: theme.colors.onSurface }]}>
              {action.title}
            </Text>
            <Text variant="bodySmall" style={[styles.actionSubtitle, { color: theme.colors.onSurfaceVariant }]}>
              {action.subtitle}
            </Text>
          </View>

          <Icon
            name="chevron-right"
            size={20}
            color={theme.colors.onSurfaceVariant}
          />
        </View>
      </TouchableRipple>
    </Surface>
  );

  return (
    <CustomBottomSheet
      ref={bottomSheetRef}
      snapPoints={['50%', '80%']}
      title="Quick Actions"
      subtitle="Common tasks and shortcuts"
      icon="lightning-bolt"
      iconColor="#F59E0B"
      iconBackground="#F59E0B15"
    >
      <View style={styles.actionsGrid}>
        {actions.map(renderActionItem)}
      </View>

      {/* Quick Stats Section */}
      <Surface style={[styles.statsSection, { backgroundColor: theme.colors.surfaceVariant }]} elevation={1}>
        <View style={styles.sectionHeader}>
          <Icon name="speedometer" size={20} color={theme.colors.primary} />
          <Text variant="titleMedium" style={styles.sectionTitle}>Today's Overview</Text>
        </View>

        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
              12
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Orders
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.secondary, fontWeight: '700' }}>
              $340
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Revenue
            </Text>
          </View>

          <View style={styles.statItem}>
            <Text variant="headlineSmall" style={{ color: theme.colors.tertiary, fontWeight: '700' }}>
              85%
            </Text>
            <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
              Stock Level
            </Text>
          </View>
        </View>
      </Surface>
    </CustomBottomSheet>
  );
});

const styles = StyleSheet.create({
  actionsGrid: {
    gap: 8,
    marginBottom: 16,
  },
  actionItem: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  actionContent: {
    borderRadius: 12,
  },
  actionInner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionText: {
    flex: 1,
  },
  actionTitle: {
    fontWeight: '600',
    marginBottom: 2,
  },
  actionSubtitle: {
    lineHeight: 16,
  },
  statsSection: {
    borderRadius: 12,
    padding: 16,
    marginTop: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
    gap: 8,
  },
  sectionTitle: {
    fontWeight: '600',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
});

QuickActionsBottomSheet.displayName = 'QuickActionsBottomSheet';

export default QuickActionsBottomSheet;
