/**
 * Quick Actions BottomSheet - Example using CustomBottomSheet
 * Shows common actions that can be performed quickly
 */

import React, { forwardRef } from 'react';
import { View, StyleSheet } from 'react-native';
import {
  Text,
  Surface,
  useTheme,
  TouchableRipple
} from 'react-native-paper';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import CustomBottomSheet from './CustomBottomSheet';

const QuickActionsBottomSheet = forwardRef(({ onActionPress }, ref) => {
  const theme = useTheme();
  const bottomSheetRef = React.useRef(null);

  React.useImperativeHandle(ref, () => ({
    open: () => {
      console.log('⚡ Opening Quick Actions bottomsheet');
      bottomSheetRef.current?.expand();
    },
    close: () => {
      console.log('⚡ Closing Quick Actions bottomsheet');
      bottomSheetRef.current?.close();
    }
  }));

  const actions = [
    {
      id: 'add-product',
      title: 'Add Product',
      subtitle: 'Create a new product',
      icon: 'plus-circle',
      color: theme.colors.primary,
      backgroundColor: theme.colors.primaryContainer,
    },
    {
      id: 'add-order',
      title: 'New Order',
      subtitle: 'Create customer order',
      icon: 'clipboard-plus',
      color: theme.colors.secondary,
      backgroundColor: theme.colors.secondaryContainer,
    },
    {
      id: 'add-customer',
      title: 'Add Customer',
      subtitle: 'Register new customer',
      icon: 'account-plus',
      color: theme.colors.tertiary,
      backgroundColor: theme.colors.tertiaryContainer,
    },
  ];

  const handleActionPress = (actionId) => {
    console.log(`⚡ Quick Action pressed: ${actionId}`);
    bottomSheetRef.current?.close();
    onActionPress?.(actionId);
  };

  const renderActionItem = (action) => (
    <Surface
      key={action.id}
      style={[styles.actionItem, { backgroundColor: theme.colors.surface }]}
      elevation={1}
    >
      <TouchableRipple
        onPress={() => handleActionPress(action.id)}
        style={styles.actionContent}
        borderless
      >
        <View style={styles.actionInner}>
          <View style={[styles.iconContainer, { backgroundColor: action.backgroundColor }]}>
            <Icon name={action.icon} size={24} color={action.color} />
          </View>

          <View style={styles.actionText}>
            <Text variant="titleMedium" style={[styles.actionTitle, { color: theme.colors.onSurface }]}>
              {action.title}
            </Text>
            <Text variant="bodySmall" style={[styles.actionSubtitle, { color: theme.colors.onSurfaceVariant }]}>
              {action.subtitle}
            </Text>
          </View>

          <Icon
            name="chevron-right"
            size={20}
            color={theme.colors.onSurfaceVariant}
          />
        </View>
      </TouchableRipple>
    </Surface>
  );

  return (
    <CustomBottomSheet
      ref={bottomSheetRef}
      snapPoints={['50%', '80%']}
      title="Quick Actions"
      subtitle="Common tasks and shortcuts"
      icon="lightning-bolt"
      iconColor="#F59E0B"
      iconBackground="#F59E0B15"
    >
      <View style={styles.actionsGrid}>
        {actions.map(renderActionItem)}
      </View>
    </CustomBottomSheet>
  );
});

const styles = StyleSheet.create({
  actionsGrid: {
    gap: 8,
    marginBottom: 16,
  },
  actionItem: {
    borderRadius: 12,
    overflow: 'hidden',
  },
  actionContent: {
    borderRadius: 12,
  },
  actionInner: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 16,
  },
  actionText: {
    flex: 1,
  },
  actionTitle: {
    fontWeight: '600',
    marginBottom: 2,
  },
  actionSubtitle: {
    lineHeight: 16,
  },

});

QuickActionsBottomSheet.displayName = 'QuickActionsBottomSheet';

export default QuickActionsBottomSheet;
