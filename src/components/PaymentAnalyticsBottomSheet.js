import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { View, StyleSheet, ScrollView, Dimensions } from 'react-native';
import {
  Text,
  Button,
  useTheme,
  Card,
  DataTable,
  ProgressBar,
} from 'react-native-paper';
import CustomBottomSheet from './CustomBottomSheet';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
// import { PieChart } from 'react-native-chart-kit'; // Commented out for now
import { FINANCIAL_CONFIG } from '../config/constants';

const PaymentAnalyticsBottomSheet = forwardRef(({ data }, ref) => {
  const theme = useTheme();
  const bottomSheetRef = React.useRef(null);

  useImperativeHandle(ref, () => ({
    expand: () => {
      bottomSheetRef.current?.expand();
    },
    close: () => {
      bottomSheetRef.current?.close();
    },
  }));

  const formatCurrency = (amount) => {
    return `${FINANCIAL_CONFIG.CURRENCY.SYMBOL}${amount.toFixed(FINANCIAL_CONFIG.CURRENCY.DECIMAL_PLACES)}`;
  };

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`;
  };

  const getChartData = () => {
    if (!data?.byPaymentMethod) return [];

    const colors = [
      '#4CAF50', '#2196F3', '#FF9800', '#9C27B0',
      '#F44336', '#00BCD4', '#795548', '#607D8B'
    ];

    return Object.entries(data.byPaymentMethod)
      .filter(([_, methodData]) => methodData.totalAmount > 0)
      .map(([method, methodData], index) => ({
        name: method,
        amount: methodData.totalAmount,
        percentage: methodData.percentage,
        color: colors[index % colors.length],
        legendFontColor: theme.colors.onSurface,
        legendFontSize: 12,
      }));
  };

  const getMostPopularPaymentMethod = () => {
    if (!data?.byPaymentMethod) return null;

    return Object.entries(data.byPaymentMethod)
      .reduce((max, [method, methodData]) =>
        methodData.totalAmount > (max?.data?.totalAmount || 0)
          ? { method, data: methodData }
          : max,
        null
      );
  };

  if (!data) return null;

  const chartData = getChartData();
  const mostPopular = getMostPopularPaymentMethod();
  const screenWidth = Dimensions.get('window').width;

  const footerButtons = (
    <Button
      mode="contained"
      onPress={() => {
        // Export functionality would go here
        console.log('Export payment analytics');
      }}
      icon="download"
      style={styles.exportButton}
    >
      Export Analytics
    </Button>
  );

  return (
    <CustomBottomSheet
      ref={bottomSheetRef}
      snapPoints={['90%']}
      title="Payment Method Analytics"
      subtitle={`${new Date(data.period.startDate).toLocaleDateString()} - ${new Date(data.period.endDate).toLocaleDateString()}`}
      icon="chart-pie"
      iconColor={theme.colors.primary}
      footerContent={footerButtons}
    >

        {/* Summary Cards */}
        <View style={styles.summaryGrid}>
          <Card style={[styles.summaryCard, { backgroundColor: theme.colors.primaryContainer }]}>
            <Card.Content style={styles.summaryContent}>
              <Icon name="cash-multiple" size={24} color={theme.colors.primary} />
              <Text variant="bodySmall" style={{ color: theme.colors.onPrimaryContainer }}>
                Total Revenue
              </Text>
              <Text variant="titleMedium" style={{ color: theme.colors.onPrimaryContainer, fontWeight: '700' }}>
                {formatCurrency(data.summary.totalRevenue)}
              </Text>
            </Card.Content>
          </Card>

          <Card style={[styles.summaryCard, { backgroundColor: theme.colors.secondaryContainer }]}>
            <Card.Content style={styles.summaryContent}>
              <Icon name="receipt" size={24} color={theme.colors.secondary} />
              <Text variant="bodySmall" style={{ color: theme.colors.onSecondaryContainer }}>
                Total Orders
              </Text>
              <Text variant="titleMedium" style={{ color: theme.colors.onSecondaryContainer, fontWeight: '700' }}>
                {data.summary.totalOrders}
              </Text>
            </Card.Content>
          </Card>

          <Card style={[styles.summaryCard, { backgroundColor: theme.colors.tertiaryContainer }]}>
            <Card.Content style={styles.summaryContent}>
              <Icon name="chart-line" size={24} color={theme.colors.tertiary} />
              <Text variant="bodySmall" style={{ color: theme.colors.onTertiaryContainer }}>
                Avg Order Value
              </Text>
              <Text variant="titleMedium" style={{ color: theme.colors.onTertiaryContainer, fontWeight: '700' }}>
                {formatCurrency(data.summary.averageOrderValue)}
              </Text>
            </Card.Content>
          </Card>
        </View>

        {/* Most Popular Payment Method */}
        {mostPopular && (
          <Card style={[styles.popularCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content>
              <View style={styles.popularHeader}>
                <Icon name="trophy" size={24} color="#FFD700" />
                <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginLeft: 8 }}>
                  Most Popular Payment Method
                </Text>
              </View>
              <View style={styles.popularContent}>
                <Text variant="headlineSmall" style={{ color: theme.colors.primary, fontWeight: '700' }}>
                  {mostPopular.method}
                </Text>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant }}>
                  {formatCurrency(mostPopular.data.totalAmount)} • {formatPercentage(mostPopular.data.percentage)} of total revenue
                </Text>
                <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                  {mostPopular.data.orderCount} orders • Avg: {formatCurrency(mostPopular.data.averageOrderValue)}
                </Text>
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Chart Placeholder - Will be implemented later */}
        {chartData.length > 0 && (
          <Card style={[styles.chartCard, { backgroundColor: theme.colors.surface }]}>
            <Card.Content>
              <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginBottom: 16 }}>
                Revenue Distribution
              </Text>
              <View style={styles.chartContainer}>
                <Text variant="bodyMedium" style={{ color: theme.colors.onSurfaceVariant, textAlign: 'center' }}>
                  Chart visualization will be available in the next update
                </Text>
              </View>
            </Card.Content>
          </Card>
        )}

        {/* Detailed Breakdown */}
        <Card style={[styles.detailCard, { backgroundColor: theme.colors.surface }]}>
          <Card.Content>
            <Text variant="titleMedium" style={{ color: theme.colors.onSurface, marginBottom: 16 }}>
              Detailed Breakdown
            </Text>

            <DataTable>
              <DataTable.Header>
                <DataTable.Title>Payment Method</DataTable.Title>
                <DataTable.Title numeric>Orders</DataTable.Title>
                <DataTable.Title numeric>Amount</DataTable.Title>
                <DataTable.Title numeric>%</DataTable.Title>
              </DataTable.Header>

              {Object.entries(data.byPaymentMethod)
                .filter(([_, methodData]) => methodData.totalAmount > 0)
                .sort(([,a], [,b]) => b.totalAmount - a.totalAmount)
                .map(([method, methodData]) => (
                  <DataTable.Row key={method}>
                    <DataTable.Cell>
                      <View style={styles.methodCell}>
                        <View style={[
                          styles.methodIndicator,
                          { backgroundColor: chartData.find(d => d.name === method)?.color || theme.colors.primary }
                        ]} />
                        <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                          {method}
                        </Text>
                      </View>
                    </DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                        {methodData.orderCount}
                      </Text>
                    </DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                        {formatCurrency(methodData.totalAmount)}
                      </Text>
                    </DataTable.Cell>
                    <DataTable.Cell numeric>
                      <Text variant="bodyMedium" style={{ color: theme.colors.onSurface }}>
                        {formatPercentage(methodData.percentage)}
                      </Text>
                    </DataTable.Cell>
                  </DataTable.Row>
                ))}
            </DataTable>

            {/* Progress Bars */}
            <View style={styles.progressSection}>
              <Text variant="titleSmall" style={{ color: theme.colors.onSurface, marginBottom: 12 }}>
                Revenue Share
              </Text>
              {Object.entries(data.byPaymentMethod)
                .filter(([_, methodData]) => methodData.totalAmount > 0)
                .sort(([,a], [,b]) => b.totalAmount - a.totalAmount)
                .map(([method, methodData]) => (
                  <View key={method} style={styles.progressItem}>
                    <View style={styles.progressHeader}>
                      <Text variant="bodySmall" style={{ color: theme.colors.onSurface }}>
                        {method}
                      </Text>
                      <Text variant="bodySmall" style={{ color: theme.colors.onSurfaceVariant }}>
                        {formatPercentage(methodData.percentage)}
                      </Text>
                    </View>
                    <ProgressBar
                      progress={methodData.percentage / 100}
                      color={chartData.find(d => d.name === method)?.color || theme.colors.primary}
                      style={styles.progressBar}
                    />
                  </View>
                ))}
            </View>
          </Card.Content>
        </Card>
    </CustomBottomSheet>
  );
});

const styles = StyleSheet.create({
  summaryGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },
  summaryCard: {
    flex: 1,
    minWidth: '30%',
    borderRadius: 12,
  },
  summaryContent: {
    alignItems: 'center',
    gap: 4,
  },
  popularCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  popularHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  popularContent: {
    alignItems: 'center',
    gap: 4,
  },
  chartCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  chartContainer: {
    alignItems: 'center',
  },
  detailCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  methodCell: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  methodIndicator: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  progressSection: {
    marginTop: 16,
  },
  progressItem: {
    marginBottom: 12,
  },
  progressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  progressBar: {
    height: 6,
    borderRadius: 3,
  },
  exportButton: {
    borderRadius: 25,
  },
});

export default PaymentAnalyticsBottomSheet;
