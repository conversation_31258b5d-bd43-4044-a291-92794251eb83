import React, { createContext, useContext, useReducer, useEffect, useMemo, useCallback } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Initial state with empty arrays - data will be loaded from storage
const initialState = {
  products: [],
  orders: [],
  customers: [],
  settings: {
    storeName: 'Sweet Delights Bakery',
    ownerName: '<PERSON>',
    email: '<EMAIL>',
    phone: '******-123-4567',
    address: '123 Baker Street, Sweet City, SC 12345',
    taxRate: 0.08,
    currency: 'USD',
    notifications: true,
    darkMode: false,
    autoBackup: true,
    storeHours: {
      monday: { open: '09:00', close: '18:00', closed: false },
      tuesday: { open: '09:00', close: '18:00', closed: false },
      wednesday: { open: '09:00', close: '18:00', closed: false },
      thursday: { open: '09:00', close: '18:00', closed: false },
      friday: { open: '09:00', close: '18:00', closed: false },
      saturday: { open: '09:00', close: '17:00', closed: false },
      sunday: { open: '10:00', close: '16:00', closed: true },
    },
    paymentMethods: {
      cash: { enabled: true, processingFee: 0 },
      card: { enabled: true, processingFee: 2.9 },
      digitalWallet: { enabled: false, processingFee: 2.5 },
      bankTransfer: { enabled: false, processingFee: 1.0 },
      giftCard: { enabled: true, processingFee: 0 },
    },
  },
  nextProductId: 1,
  nextOrderId: 1,
  nextCustomerId: 1,
  isDataLoaded: false, // Track if data has been loaded from storage
};

// Action types
const actionTypes = {
  // Products
  ADD_PRODUCT: 'ADD_PRODUCT',
  UPDATE_PRODUCT: 'UPDATE_PRODUCT',
  DELETE_PRODUCT: 'DELETE_PRODUCT',
  UPDATE_STOCK: 'UPDATE_STOCK',

  // Orders
  ADD_ORDER: 'ADD_ORDER',
  UPDATE_ORDER: 'UPDATE_ORDER',
  DELETE_ORDER: 'DELETE_ORDER',
  UPDATE_ORDER_STATUS: 'UPDATE_ORDER_STATUS',

  // Customers
  ADD_CUSTOMER: 'ADD_CUSTOMER',
  UPDATE_CUSTOMER: 'UPDATE_CUSTOMER',
  DELETE_CUSTOMER: 'DELETE_CUSTOMER',

  // Settings
  UPDATE_SETTINGS: 'UPDATE_SETTINGS',

  // Data
  LOAD_DATA: 'LOAD_DATA',
  SET_DATA_LOADED: 'SET_DATA_LOADED',
  CLEAR_DATA: 'CLEAR_DATA',
};

// Reducer function
const dataReducer = (state, action) => {
  switch (action.type) {
    case actionTypes.ADD_PRODUCT:
      return {
        ...state,
        products: [...state.products, { ...action.payload, id: state.nextProductId }],
        nextProductId: state.nextProductId + 1,
      };

    case actionTypes.UPDATE_PRODUCT:
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id ? { ...product, ...action.payload } : product
        ),
      };

    case actionTypes.DELETE_PRODUCT:
      return {
        ...state,
        products: state.products.filter(product => product.id !== action.payload),
      };

    case actionTypes.UPDATE_STOCK:
      return {
        ...state,
        products: state.products.map(product =>
          product.id === action.payload.id
            ? { ...product, stock: Math.max(0, product.stock + action.payload.change) }
            : product
        ),
      };

    case actionTypes.ADD_ORDER:
      const newOrderId = String(state.nextOrderId).padStart(3, '0');
      return {
        ...state,
        orders: [...state.orders, { ...action.payload, id: newOrderId }],
        nextOrderId: state.nextOrderId + 1,
      };

    case actionTypes.UPDATE_ORDER:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, ...action.payload } : order
        ),
      };

    case actionTypes.DELETE_ORDER:
      return {
        ...state,
        orders: state.orders.filter(order => order.id !== action.payload),
      };

    case actionTypes.UPDATE_ORDER_STATUS:
      return {
        ...state,
        orders: state.orders.map(order =>
          order.id === action.payload.id ? { ...order, status: action.payload.status } : order
        ),
      };

    case actionTypes.ADD_CUSTOMER:
      return {
        ...state,
        customers: [...state.customers, { ...action.payload, id: state.nextCustomerId }],
        nextCustomerId: state.nextCustomerId + 1,
      };

    case actionTypes.UPDATE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.map(customer =>
          customer.id === action.payload.id ? { ...customer, ...action.payload } : customer
        ),
      };

    case actionTypes.DELETE_CUSTOMER:
      return {
        ...state,
        customers: state.customers.filter(customer => customer.id !== action.payload),
      };

    case actionTypes.UPDATE_SETTINGS:
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
      };

    case actionTypes.LOAD_DATA:
      return {
        ...state,
        ...action.payload,
        isDataLoaded: true,
      };

    case actionTypes.SET_DATA_LOADED:
      return {
        ...state,
        isDataLoaded: action.payload,
      };

    case actionTypes.CLEAR_DATA:
      return {
        ...initialState,
        isDataLoaded: false,
      };

    default:
      return state;
  }
};

// Create context
const DataContext = createContext();

// Provider component with performance optimizations
export const DataProvider = ({ children }) => {
  const [state, dispatch] = useReducer(dataReducer, initialState);

  // Load data from AsyncStorage on app start
  useEffect(() => {
    loadData();
  }, []);

  // Debounced save to prevent excessive writes
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      saveData();
    }, 1000); // Save after 1 second of inactivity

    return () => clearTimeout(timeoutId);
  }, [state.products, state.orders, state.customers, state.settings]); // Only watch specific state properties

  const loadData = useCallback(async () => {
    try {
      console.log('Loading data from storage...');
      const savedData = await AsyncStorage.getItem('bakeryData');

      if (savedData) {
        const parsedData = JSON.parse(savedData);

        // Validate data structure to prevent corruption
        if (parsedData && typeof parsedData === 'object') {
          // Ensure all required fields exist with defaults
          const validatedData = {
            products: Array.isArray(parsedData.products) ? parsedData.products : [],
            orders: Array.isArray(parsedData.orders) ? parsedData.orders : [],
            customers: Array.isArray(parsedData.customers) ? parsedData.customers : [],
            settings: parsedData.settings || initialState.settings,
            nextProductId: parsedData.nextProductId || (parsedData.products?.length ? Math.max(...parsedData.products.map(p => p.id)) + 1 : 1),
            nextOrderId: parsedData.nextOrderId || (parsedData.orders?.length ? Math.max(...parsedData.orders.map(o => parseInt(o.id))) + 1 : 1),
            nextCustomerId: parsedData.nextCustomerId || (parsedData.customers?.length ? Math.max(...parsedData.customers.map(c => c.id)) + 1 : 1),
          };

          dispatch({ type: actionTypes.LOAD_DATA, payload: validatedData });
          console.log(`Data loaded successfully: ${validatedData.products.length} products, ${validatedData.orders.length} orders, ${validatedData.customers.length} customers`);
        } else {
          console.warn('Invalid data structure found in storage, using empty state');
          dispatch({ type: actionTypes.SET_DATA_LOADED, payload: true });
        }
      } else {
        console.log('No saved data found, starting with empty state');
        dispatch({ type: actionTypes.SET_DATA_LOADED, payload: true });
      }
    } catch (error) {
      console.error('Error loading data from storage:', error);
      dispatch({ type: actionTypes.SET_DATA_LOADED, payload: true });
    }
  }, []);

  const saveData = useCallback(async () => {
    // Only save if data has been loaded to prevent overwriting with empty state
    if (!state.isDataLoaded) {
      return;
    }

    try {
      const dataToSave = {
        products: state.products || [],
        orders: state.orders || [],
        customers: state.customers || [],
        settings: state.settings || initialState.settings,
        nextProductId: state.nextProductId || 1,
        nextOrderId: state.nextOrderId || 1,
        nextCustomerId: state.nextCustomerId || 1,
        lastSaved: new Date().toISOString(),
      };

      await AsyncStorage.setItem('bakeryData', JSON.stringify(dataToSave));
      console.log(`Data saved successfully: ${dataToSave.products.length} products, ${dataToSave.orders.length} orders, ${dataToSave.customers.length} customers`);
    } catch (error) {
      console.error('Error saving data to storage:', error);
      // Don't crash the app on save errors
    }
  }, [state.products, state.orders, state.customers, state.settings, state.nextProductId, state.nextOrderId, state.nextCustomerId, state.isDataLoaded]);

  // Memoized action creators for performance
  const actions = useMemo(() => ({
    // Products
    addProduct: (product) => dispatch({ type: actionTypes.ADD_PRODUCT, payload: product }),
    updateProduct: (product) => dispatch({ type: actionTypes.UPDATE_PRODUCT, payload: product }),
    deleteProduct: (id) => dispatch({ type: actionTypes.DELETE_PRODUCT, payload: id }),
    updateStock: (id, change) => dispatch({ type: actionTypes.UPDATE_STOCK, payload: { id, change } }),

    // Orders
    addOrder: (order) => dispatch({ type: actionTypes.ADD_ORDER, payload: order }),
    updateOrder: (order) => dispatch({ type: actionTypes.UPDATE_ORDER, payload: order }),
    deleteOrder: (id) => dispatch({ type: actionTypes.DELETE_ORDER, payload: id }),
    updateOrderStatus: (id, status) => dispatch({ type: actionTypes.UPDATE_ORDER_STATUS, payload: { id, status } }),

    // Customers
    addCustomer: (customer) => dispatch({ type: actionTypes.ADD_CUSTOMER, payload: customer }),
    updateCustomer: (customer) => dispatch({ type: actionTypes.UPDATE_CUSTOMER, payload: customer }),
    deleteCustomer: (id) => dispatch({ type: actionTypes.DELETE_CUSTOMER, payload: id }),

    // Settings
    updateSettings: (settings) => dispatch({ type: actionTypes.UPDATE_SETTINGS, payload: settings }),

    // Data Management
    clearAllData: async () => {
      try {
        await AsyncStorage.removeItem('bakeryData');
        dispatch({ type: actionTypes.CLEAR_DATA });
        console.log('All data cleared successfully');
      } catch (error) {
        console.error('Error clearing data:', error);
      }
    },

    importData: (importedData) => {
      try {
        // Validate imported data
        const validatedData = {
          products: Array.isArray(importedData.products) ? importedData.products : [],
          orders: Array.isArray(importedData.orders) ? importedData.orders : [],
          customers: Array.isArray(importedData.customers) ? importedData.customers : [],
          settings: importedData.settings || state.settings,
        };

        // Update next IDs based on imported data
        const nextProductId = validatedData.products.length > 0 ? Math.max(...validatedData.products.map(p => p.id)) + 1 : 1;
        const nextOrderId = validatedData.orders.length > 0 ? Math.max(...validatedData.orders.map(o => parseInt(o.id))) + 1 : 1;
        const nextCustomerId = validatedData.customers.length > 0 ? Math.max(...validatedData.customers.map(c => c.id)) + 1 : 1;

        dispatch({
          type: actionTypes.LOAD_DATA,
          payload: {
            ...validatedData,
            nextProductId,
            nextOrderId,
            nextCustomerId,
          }
        });

        console.log(`Data imported successfully: ${validatedData.products.length} products, ${validatedData.orders.length} orders, ${validatedData.customers.length} customers`);
      } catch (error) {
        console.error('Error importing data:', error);
      }
    },

    exportData: () => {
      return {
        products: state.products,
        orders: state.orders,
        customers: state.customers,
        settings: state.settings,
        exportDate: new Date().toISOString(),
        version: '1.0.0',
      };
    },

    reloadData: async () => {
      await loadData();
    },
  }), [dispatch]);

  return (
    <DataContext.Provider value={{ state, actions }}>
      {children}
    </DataContext.Provider>
  );
};

// Custom hook to use the context
export const useData = () => {
  const context = useContext(DataContext);
  if (!context) {
    throw new Error('useData must be used within a DataProvider');
  }
  return context;
};

export default DataContext;
