/**
 * TabNavigationContext - Provides tab navigation functionality to all screens
 */

import React, { createContext, useContext, useState } from 'react';

const TabNavigationContext = createContext();

export const useTabNavigation = () => {
  const context = useContext(TabNavigationContext);
  if (!context) {
    throw new Error('useTabNavigation must be used within a TabNavigationProvider');
  }
  return context;
};

export const TabNavigationProvider = ({ children }) => {
  const [currentTab, setCurrentTab] = useState('Dashboard');

  const navigateToTab = (tabName) => {
    console.log(`TabNavigationContext: Navigating to tab ${tabName}`);
    setCurrentTab(tabName);
  };

  const value = {
    currentTab,
    setCurrentTab,
    navigateToTab,
  };

  return (
    <TabNavigationContext.Provider value={value}>
      {children}
    </TabNavigationContext.Provider>
  );
};

export default TabNavigationContext;
