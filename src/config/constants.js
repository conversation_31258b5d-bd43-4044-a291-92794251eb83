// Application Constants
export const APP_CONFIG = {
  name: 'Bakery Management',
  version: '1.0.0',
  author: 'Your Company',
  supportEmail: '<EMAIL>',
};

// API Configuration
export const API_CONFIG = {
  baseURL: process.env.EXPO_PUBLIC_API_URL || 'https://api.yourapp.com',
  timeout: 10000,
  retryAttempts: 3,
};

// Storage Keys
export const STORAGE_KEYS = {
  THEME: 'darkMode',
  USER_DATA: 'userData',
  BAKERY_DATA: 'bakeryData',
  SETTINGS: 'appSettings',
  CACHE: 'appCache',
};

// Business Rules
export const BUSINESS_RULES = {
  MAX_PRODUCTS: 1000,
  MAX_ORDERS_PER_DAY: 500,
  MIN_STOCK_ALERT: 5,
  MAX_DISCOUNT_PERCENT: 50,
  ORDER_STATUSES: ['Pending', 'In Progress', 'Completed', 'Cancelled'],
  PRODUCT_CATEGORIES: [
    'Breads', 'Pastries', 'Cakes', 'Cookies', 'Beverages', 'Sandwiches', 'Other'
  ],
  PAYMENT_METHODS: ['Cash', 'Card', 'Digital Wallet', 'Bank Transfer'],
};

// UI Constants
export const UI_CONSTANTS = {
  ANIMATION_DURATION: 300,
  DEBOUNCE_DELAY: 500,
  PAGINATION_SIZE: 20,
  MAX_SEARCH_RESULTS: 50,
  BOTTOM_SHEET_SNAP_POINTS: ['25%', '50%', '90%'],
};

// Validation Rules
export const VALIDATION_RULES = {
  PRODUCT_NAME: {
    minLength: 2,
    maxLength: 100,
    required: true,
  },
  PRICE: {
    min: 0.01,
    max: 9999.99,
    required: true,
  },
  STOCK: {
    min: 0,
    max: 10000,
    required: true,
  },
  CUSTOMER_NAME: {
    minLength: 2,
    maxLength: 50,
    required: true,
  },
  PHONE: {
    pattern: /^[\+]?[1-9][\d]{0,15}$/,
    required: false,
  },
  EMAIL: {
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    required: false,
  },
};

// Financial Management
export const FINANCIAL_CONFIG = {
  EXPENSE_CATEGORIES: [
    'Ingredients',
    'Utilities',
    'Staff Wages',
    'Rent',
    'Equipment',
    'Marketing',
    'Insurance',
    'Supplies',
    'Maintenance',
    'Other'
  ],
  PAYMENT_METHODS: [
    'Cash',
    'Credit Card',
    'Debit Card',
    'Digital Wallet',
    'Bank Transfer',
    'Check',
    'Store Credit'
  ],
  TAX_RATES: {
    SALES_TAX: 0.08, // 8% default sales tax
    INCOME_TAX: 0.21, // 21% corporate tax rate
    PAYROLL_TAX: 0.153, // 15.3% payroll tax
  },
  CURRENCY: {
    SYMBOL: '$',
    CODE: 'USD',
    DECIMAL_PLACES: 2,
  },
  FISCAL_YEAR_START: 1, // January (1-12)
  RECONCILIATION_TOLERANCE: 5.00, // $5 tolerance for cash reconciliation
};

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_ANALYTICS: true,
  ENABLE_PUSH_NOTIFICATIONS: true,
  ENABLE_OFFLINE_MODE: true,
  ENABLE_MULTI_STORE: false,
  ENABLE_INVENTORY_TRACKING: true,
  ENABLE_CUSTOMER_LOYALTY: false,
  ENABLE_ADVANCED_REPORTS: true,
  ENABLE_FINANCIAL_MANAGEMENT: true,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  SAVE_ERROR: 'Failed to save data. Please try again.',
  LOAD_ERROR: 'Failed to load data. Please refresh the app.',
  PERMISSION_ERROR: 'Permission denied. Please check app permissions.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  SAVE_SUCCESS: 'Data saved successfully!',
  UPDATE_SUCCESS: 'Updated successfully!',
  DELETE_SUCCESS: 'Deleted successfully!',
  EXPORT_SUCCESS: 'Data exported successfully!',
  IMPORT_SUCCESS: 'Data imported successfully!',
};
