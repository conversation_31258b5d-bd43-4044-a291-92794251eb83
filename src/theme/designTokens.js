/**
 * Design Tokens - Centralized design system values
 * All spacing, sizing, typography, and other design values should be defined here
 */

// Spacing System (8pt grid)
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 12,
  lg: 16,
  xl: 20,
  xxl: 24,
  xxxl: 32,
  xxxxl: 40,
  xxxxxl: 48,
  xxxxxxl: 64,
};

// Border Radius System
export const BORDER_RADIUS = {
  xs: 4,
  sm: 6,
  md: 8,
  lg: 10,
  xl: 12,
  xxl: 16,
  xxxl: 20,
  round: 25,
  circle: 50,
};

// Typography System
export const TYPOGRAPHY = {
  fontSize: {
    xs: 10,
    sm: 12,
    md: 14,
    lg: 16,
    xl: 18,
    xxl: 20,
    xxxl: 24,
    xxxxl: 28,
    xxxxxl: 32,
    xxxxxxl: 36,
  },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
};

// Shadow System
export const SHADOWS = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 12,
  },
};

// Component Sizing
export const COMPONENT_SIZES = {
  button: {
    small: { height: 32, paddingHorizontal: 12 },
    medium: { height: 40, paddingHorizontal: 16 },
    large: { height: 48, paddingHorizontal: 20 },
    xlarge: { height: 56, paddingHorizontal: 24 },
  },
  input: {
    small: { height: 32 },
    medium: { height: 40 },
    large: { height: 48 },
    xlarge: { height: 56 },
  },
  icon: {
    xs: 12,
    sm: 16,
    md: 20,
    lg: 24,
    xl: 28,
    xxl: 32,
    xxxl: 40,
    xxxxl: 48,
  },
  avatar: {
    xs: 24,
    sm: 32,
    md: 40,
    lg: 48,
    xl: 56,
    xxl: 64,
    xxxl: 80,
    xxxxl: 96,
  },
};

// Layout Constants
export const LAYOUT = {
  headerHeight: 64,
  bottomNavHeight: 80,
  tabBarHeight: 48,
  statusBarHeight: 44, // iOS default
  safeAreaPadding: 16,
  screenPadding: 16,
  cardPadding: 16,
  sectionSpacing: 24,
  itemSpacing: 12,
};

// Animation Constants
export const ANIMATIONS = {
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
    verySlow: 800,
  },
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
};

// Opacity Values
export const OPACITY = {
  disabled: 0.4,
  muted: 0.6,
  subtle: 0.8,
  overlay: 0.5,
  backdrop: 0.3,
};

// Z-Index System
export const Z_INDEX = {
  base: 0,
  dropdown: 10,
  sticky: 20,
  modal: 30,
  popover: 40,
  tooltip: 50,
  notification: 60,
  overlay: 70,
  max: 9999,
};

// Breakpoints (for responsive design)
export const BREAKPOINTS = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1400,
};

// Common Component Styles
export const COMMON_STYLES = {
  card: {
    borderRadius: BORDER_RADIUS.xl,
    padding: SPACING.lg,
    marginBottom: SPACING.md,
    borderWidth: 1,
  },
  button: {
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    borderRadius: BORDER_RADIUS.lg,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderWidth: 1,
  },
  section: {
    marginBottom: SPACING.xxl,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  column: {
    flexDirection: 'column',
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  flex1: {
    flex: 1,
  },
};

// Helper function to get theme-aware border color
export const getBorderColor = (theme, opacity = 0.05) => {
  return theme.colors.onSurface + Math.round(opacity * 255).toString(16).padStart(2, '0');
};

// Helper function to get theme-aware shadow
export const getThemedShadow = (shadowLevel, theme) => {
  const shadow = SHADOWS[shadowLevel] || SHADOWS.md;
  return {
    ...shadow,
    shadowColor: theme.colors.onSurface,
  };
};

export default {
  SPACING,
  BORDER_RADIUS,
  TYPOGRAPHY,
  SHADOWS,
  COMPONENT_SIZES,
  LAYOUT,
  ANIMATIONS,
  OPACITY,
  Z_INDEX,
  BREAKPOINTS,
  COMMON_STYLES,
  getBorderColor,
  getThemedShadow,
};
