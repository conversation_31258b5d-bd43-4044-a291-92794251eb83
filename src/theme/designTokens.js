/**
 * Design Tokens - Centralized design system values
 * All spacing, sizing, typography, and other design values should be defined here
 */

// Spacing System (Compact 4pt grid)
export const SPACING = {
  xs: 2,
  sm: 4,
  md: 6,
  lg: 8,
  xl: 12,
  xxl: 16,
  xxxl: 20,
  xxxxl: 24,
  xxxxxl: 32,
  xxxxxxl: 40,
};

// Border Radius System
export const BORDER_RADIUS = {
  xs: 4,
  sm: 6,
  md: 8,
  lg: 10,
  xl: 12,
  xxl: 16,
  xxxl: 20,
  round: 25,
  circle: 50,
};

// Typography System
export const TYPOGRAPHY = {
  fontSize: {
    xs: 10,
    sm: 12,
    md: 14,
    lg: 16,
    xl: 18,
    xxl: 20,
    xxxl: 24,
    xxxxl: 28,
    xxxxxl: 32,
    xxxxxxl: 36,
  },
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
};

// Shadow System
export const SHADOWS = {
  none: {
    shadowColor: 'transparent',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  xs: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 1,
    elevation: 1,
  },
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 12,
  },
};

// Component Sizing (Compact)
export const COMPONENT_SIZES = {
  button: {
    small: { height: 28, paddingHorizontal: 8 },
    medium: { height: 32, paddingHorizontal: 12 },
    large: { height: 36, paddingHorizontal: 16 },
    xlarge: { height: 40, paddingHorizontal: 20 },
  },
  input: {
    small: { height: 28 },
    medium: { height: 32 },
    large: { height: 36 },
    xlarge: { height: 40 },
  },
  icon: {
    xs: 10,
    sm: 12,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 28,
    xxxxl: 32,
  },
  avatar: {
    xs: 20,
    sm: 24,
    md: 28,
    lg: 32,
    xl: 36,
    xxl: 40,
    xxxl: 48,
    xxxxl: 56,
  },
};

// Layout Constants (Compact)
export const LAYOUT = {
  headerHeight: 52,
  bottomNavHeight: 64,
  tabBarHeight: 40,
  statusBarHeight: 44, // iOS default
  safeAreaPadding: 8,
  screenPadding: 8,
  cardPadding: 8,
  sectionSpacing: 12,
  itemSpacing: 6,
};

// Animation Constants
export const ANIMATIONS = {
  duration: {
    fast: 150,
    normal: 300,
    slow: 500,
    verySlow: 800,
  },
  easing: {
    linear: 'linear',
    ease: 'ease',
    easeIn: 'ease-in',
    easeOut: 'ease-out',
    easeInOut: 'ease-in-out',
  },
};

// Opacity Values
export const OPACITY = {
  disabled: 0.4,
  muted: 0.6,
  subtle: 0.8,
  overlay: 0.5,
  backdrop: 0.3,
};

// Z-Index System
export const Z_INDEX = {
  base: 0,
  dropdown: 10,
  sticky: 20,
  modal: 30,
  popover: 40,
  tooltip: 50,
  notification: 60,
  overlay: 70,
  max: 9999,
};

// Breakpoints (for responsive design)
export const BREAKPOINTS = {
  xs: 0,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1400,
};

// Common Component Styles (Compact)
export const COMMON_STYLES = {
  card: {
    borderRadius: BORDER_RADIUS.lg,
    padding: SPACING.lg,
    marginBottom: SPACING.sm,
    borderWidth: 1,
  },
  button: {
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    justifyContent: 'center',
    alignItems: 'center',
  },
  input: {
    borderRadius: BORDER_RADIUS.md,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.sm,
    borderWidth: 1,
  },
  section: {
    marginBottom: SPACING.xl,
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  column: {
    flexDirection: 'column',
  },
  center: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  spaceBetween: {
    justifyContent: 'space-between',
  },
  flex1: {
    flex: 1,
  },
};

// Helper function to get theme-aware border color
export const getBorderColor = (theme, opacity = 0.05) => {
  return theme.colors.onSurface + Math.round(opacity * 255).toString(16).padStart(2, '0');
};

// Helper function to get theme-aware shadow
export const getThemedShadow = (shadowLevel, theme) => {
  const shadow = SHADOWS[shadowLevel] || SHADOWS.md;
  return {
    ...shadow,
    shadowColor: theme.colors.onSurface,
  };
};

export default {
  SPACING,
  BORDER_RADIUS,
  TYPOGRAPHY,
  SHADOWS,
  COMPONENT_SIZES,
  LAYOUT,
  ANIMATIONS,
  OPACITY,
  Z_INDEX,
  BREAKPOINTS,
  COMMON_STYLES,
  getBorderColor,
  getThemedShadow,
};
