import React, { useState, useCallback, useEffect } from 'react';
import { Alert } from 'react-native';
import { StorageService } from './storageService';
import { Logger } from '../utils/errorHandler';
import { FEATURE_FLAGS } from '../config/constants';

/**
 * Notification types
 */
export const NotificationTypes = {
  LOW_STOCK: 'LOW_STOCK',
  ORDER_UPDATE: 'ORDER_UPDATE',
  DAILY_SUMMARY: 'DAILY_SUMMARY',
  SYSTEM: 'SYSTEM',
  REMINDER: 'REMINDER',
  ERROR: 'ERROR',
  SUCCESS: 'SUCCESS',
};

/**
 * Notification priorities
 */
export const NotificationPriority = {
  LOW: 'LOW',
  MEDIUM: 'MEDIUM',
  HIGH: 'HIGH',
  URGENT: 'URGENT',
};

/**
 * Notification service for managing app notifications
 */
export class NotificationService {
  static notifications = [];
  static listeners = [];
  static isEnabled = FEATURE_FLAGS.ENABLE_PUSH_NOTIFICATIONS;

  /**
   * Initialize notification service
   */
  static async initialize() {
    try {
      await this.loadNotifications();
      this.schedulePeriodicChecks();
      Logger.info('Notification service initialized');
    } catch (error) {
      Logger.error('Failed to initialize notification service', error);
    }
  }

  /**
   * Create a new notification
   */
  static async createNotification({
    id = Date.now().toString(),
    type,
    title,
    message,
    priority = NotificationPriority.MEDIUM,
    data = {},
    timestamp = new Date().toISOString(),
    read = false,
    persistent = false,
  }) {
    const notification = {
      id,
      type,
      title,
      message,
      priority,
      data,
      timestamp,
      read,
      persistent,
    };

    this.notifications.unshift(notification);
    await this.saveNotifications();
    this.notifyListeners('notification_created', notification);

    // Show immediate alert for high priority notifications
    if (priority === NotificationPriority.HIGH || priority === NotificationPriority.URGENT) {
      this.showAlert(notification);
    }

    Logger.info(`Notification created: ${title}`);
    return notification;
  }

  /**
   * Get all notifications
   */
  static getNotifications(filter = {}) {
    let filtered = [...this.notifications];

    if (filter.type) {
      filtered = filtered.filter(n => n.type === filter.type);
    }

    if (filter.unreadOnly) {
      filtered = filtered.filter(n => !n.read);
    }

    if (filter.priority) {
      filtered = filtered.filter(n => n.priority === filter.priority);
    }

    return filtered.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
  }

  /**
   * Mark notification as read
   */
  static async markAsRead(notificationId) {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification) {
      notification.read = true;
      await this.saveNotifications();
      this.notifyListeners('notification_read', notification);
    }
  }

  /**
   * Mark all notifications as read
   */
  static async markAllAsRead() {
    this.notifications.forEach(n => n.read = true);
    await this.saveNotifications();
    this.notifyListeners('all_notifications_read');
  }

  /**
   * Delete notification
   */
  static async deleteNotification(notificationId) {
    const index = this.notifications.findIndex(n => n.id === notificationId);
    if (index !== -1) {
      const notification = this.notifications[index];
      this.notifications.splice(index, 1);
      await this.saveNotifications();
      this.notifyListeners('notification_deleted', notification);
    }
  }

  /**
   * Clear all notifications
   */
  static async clearAll() {
    this.notifications = [];
    await this.saveNotifications();
    this.notifyListeners('all_notifications_cleared');
  }

  /**
   * Get unread count
   */
  static getUnreadCount() {
    return this.notifications.filter(n => !n.read).length;
  }

  /**
   * Add event listener
   */
  static addListener(callback) {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index !== -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Notify all listeners
   */
  static notifyListeners(event, data = null) {
    this.listeners.forEach(callback => {
      try {
        callback(event, data);
      } catch (error) {
        Logger.error('Error in notification listener', error);
      }
    });
  }

  /**
   * Show alert for notification
   */
  static showAlert(notification) {
    if (!this.isEnabled) return;

    Alert.alert(
      notification.title,
      notification.message,
      [
        {
          text: 'Dismiss',
          style: 'cancel',
          onPress: () => this.markAsRead(notification.id),
        },
        ...(notification.data.action ? [{
          text: notification.data.actionText || 'View',
          onPress: () => {
            this.markAsRead(notification.id);
            notification.data.action();
          },
        }] : []),
      ]
    );
  }

  /**
   * Save notifications to storage
   */
  static async saveNotifications() {
    try {
      // Keep only last 100 notifications and all persistent ones
      const toKeep = this.notifications
        .filter(n => n.persistent)
        .concat(
          this.notifications
            .filter(n => !n.persistent)
            .slice(0, 100)
        );

      this.notifications = toKeep;
      await StorageService.set('notifications', this.notifications);
    } catch (error) {
      Logger.error('Failed to save notifications', error);
    }
  }

  /**
   * Load notifications from storage
   */
  static async loadNotifications() {
    try {
      const saved = await StorageService.get('notifications') || [];
      this.notifications = saved;
    } catch (error) {
      Logger.error('Failed to load notifications', error);
      this.notifications = [];
    }
  }

  /**
   * Schedule periodic checks for business logic
   */
  static schedulePeriodicChecks() {
    // Check every 5 minutes
    setInterval(() => {
      this.checkLowStock();
      this.checkDailySummary();
    }, 5 * 60 * 1000);
  }

  /**
   * Check for low stock items
   */
  static async checkLowStock() {
    try {
      const bakeryData = await StorageService.get('bakeryData');
      if (!bakeryData?.products) return;

      const lowStockItems = bakeryData.products.filter(product => product.stock <= 5);

      if (lowStockItems.length > 0) {
        const lastCheck = await StorageService.get('lastLowStockCheck');
        const now = new Date().toDateString();

        // Only notify once per day
        if (lastCheck !== now) {
          await this.createNotification({
            type: NotificationTypes.LOW_STOCK,
            title: 'Low Stock Alert',
            message: `${lowStockItems.length} item(s) are running low on stock`,
            priority: NotificationPriority.HIGH,
            data: {
              items: lowStockItems,
              action: () => {
                // Navigate to products screen
              },
              actionText: 'View Products',
            },
          });

          await StorageService.set('lastLowStockCheck', now);
        }
      }
    } catch (error) {
      Logger.error('Failed to check low stock', error);
    }
  }

  /**
   * Check for daily summary
   */
  static async checkDailySummary() {
    try {
      const lastSummary = await StorageService.get('lastDailySummary');
      const today = new Date().toDateString();

      // Send daily summary at 6 PM
      const now = new Date();
      const isAfter6PM = now.getHours() >= 18;

      if (lastSummary !== today && isAfter6PM) {
        const bakeryData = await StorageService.get('bakeryData');
        if (!bakeryData?.orders) return;

        const todayOrders = bakeryData.orders.filter(order =>
          order.date === new Date().toLocaleDateString()
        );

        const totalSales = todayOrders.reduce((sum, order) => sum + order.total, 0);

        await this.createNotification({
          type: NotificationTypes.DAILY_SUMMARY,
          title: 'Daily Summary',
          message: `Today: ${todayOrders.length} orders, $${totalSales.toFixed(2)} in sales`,
          priority: NotificationPriority.MEDIUM,
          data: {
            orders: todayOrders.length,
            sales: totalSales,
            action: () => {
              // Navigate to reports
            },
            actionText: 'View Report',
          },
        });

        await StorageService.set('lastDailySummary', today);
      }
    } catch (error) {
      Logger.error('Failed to check daily summary', error);
    }
  }
}

/**
 * React hook for notifications
 */
export const useNotifications = () => {
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    const updateNotifications = () => {
      setNotifications(NotificationService.getNotifications());
      setUnreadCount(NotificationService.getUnreadCount());
    };

    updateNotifications();

    const unsubscribe = NotificationService.addListener(() => {
      updateNotifications();
    });

    return unsubscribe;
  }, []);

  const markAsRead = useCallback((id) => {
    NotificationService.markAsRead(id);
  }, []);

  const markAllAsRead = useCallback(() => {
    NotificationService.markAllAsRead();
  }, []);

  const deleteNotification = useCallback((id) => {
    NotificationService.deleteNotification(id);
  }, []);

  const clearAll = useCallback(() => {
    NotificationService.clearAll();
  }, []);

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    clearAll,
  };
};
