/**
 * NavigationService - Centralized navigation management
 * Provides unified navigation methods and route management
 */

import { createNavigationContainerRef } from '@react-navigation/native';

export const navigationRef = createNavigationContainerRef();

class NavigationService {
  constructor() {
    this.currentRoute = null;
    this.routeHistory = [];
    this.navigationListeners = [];
  }

  // Core navigation methods
  navigate(name, params) {
    if (navigationRef.isReady()) {
      navigationRef.navigate(name, params);
      this.addToHistory(name, params);
    }
  }

  goBack() {
    if (navigationRef.isReady() && navigationRef.canGoBack()) {
      navigationRef.goBack();
    }
  }

  reset(state) {
    if (navigationRef.isReady()) {
      navigationRef.reset(state);
      this.routeHistory = [];
    }
  }

  // Tab navigation methods
  navigateToTab(tabName) {
    this.navigate('Main', { screen: tabName });
  }

  // Modal navigation methods
  openModal(modalName, params) {
    this.navigate(modalName, params);
  }

  closeModal() {
    this.goBack();
  }

  // Settings navigation methods
  openSettings(settingsScreen = 'Profile') {
    this.navigate('SettingsStack', { screen: settingsScreen });
  }

  // Quick actions navigation
  openQuickAction(action, params) {
    switch (action) {
      case 'add-product':
        this.openModal('Products', { mode: 'add' });
        break;
      case 'add-order':
        this.navigateToTab('Orders');
        break;
      case 'scan':
        this.navigateToTab('Scan');
        break;
      case 'add-customer':
        this.navigateToTab('Dashboard');
        break;
      default:
        console.warn(`Unknown quick action: ${action}`);
    }
  }

  // Route management
  getCurrentRoute() {
    if (navigationRef.isReady()) {
      return navigationRef.getCurrentRoute();
    }
    return null;
  }

  getCurrentRouteName() {
    const route = this.getCurrentRoute();
    return route?.name || null;
  }

  addToHistory(name, params) {
    this.routeHistory.push({ name, params, timestamp: Date.now() });
    // Keep only last 10 routes
    if (this.routeHistory.length > 10) {
      this.routeHistory.shift();
    }
  }

  getRouteHistory() {
    return this.routeHistory;
  }

  // Navigation state listeners
  addNavigationListener(listener) {
    this.navigationListeners.push(listener);
  }

  removeNavigationListener(listener) {
    this.navigationListeners = this.navigationListeners.filter(l => l !== listener);
  }

  notifyNavigationChange(state) {
    this.navigationListeners.forEach(listener => listener(state));
  }

  // Utility methods
  isTabRoute(routeName) {
    const tabRoutes = ['Dashboard', 'Scan', 'Orders', 'Settings'];
    return tabRoutes.includes(routeName);
  }

  isModalRoute(routeName) {
    const modalRoutes = ['Products', 'SettingsStack'];
    return modalRoutes.includes(routeName);
  }

  getTabFromRoute(routeName) {
    if (this.isTabRoute(routeName)) {
      return routeName;
    }
    // Handle nested routes
    if (routeName === 'SettingsMain' || routeName === 'Profile') {
      return 'Settings';
    }
    return 'Dashboard'; // Default
  }

  // Deep linking support
  buildDeepLink(routeName, params) {
    let link = `sweetdelights://`;

    if (this.isTabRoute(routeName)) {
      link += `tab/${routeName.toLowerCase()}`;
    } else if (this.isModalRoute(routeName)) {
      link += `modal/${routeName.toLowerCase()}`;
    } else {
      link += `screen/${routeName.toLowerCase()}`;
    }

    if (params) {
      const queryString = Object.keys(params)
        .map(key => `${key}=${encodeURIComponent(params[key])}`)
        .join('&');
      link += `?${queryString}`;
    }

    return link;
  }

  // Navigation analytics
  getNavigationAnalytics() {
    const routeCounts = {};
    this.routeHistory.forEach(route => {
      routeCounts[route.name] = (routeCounts[route.name] || 0) + 1;
    });

    return {
      totalNavigations: this.routeHistory.length,
      routeCounts,
      mostVisitedRoute: Object.keys(routeCounts).reduce((a, b) =>
        routeCounts[a] > routeCounts[b] ? a : b, 'Dashboard'
      ),
      currentRoute: this.getCurrentRouteName(),
      history: this.routeHistory.slice(-5) // Last 5 routes
    };
  }
}

// Create singleton instance
const navigationService = new NavigationService();

export default navigationService;

// Export convenience methods
export const navigate = (name, params) => navigationService.navigate(name, params);
export const goBack = () => navigationService.goBack();
export const navigateToTab = (tabName) => navigationService.navigateToTab(tabName);
export const openModal = (modalName, params) => navigationService.openModal(modalName, params);
export const openSettings = (settingsScreen) => navigationService.openSettings(settingsScreen);
export const openQuickAction = (action, params) => navigationService.openQuickAction(action, params);
