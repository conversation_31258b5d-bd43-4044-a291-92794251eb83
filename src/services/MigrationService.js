/**
 * Migration Service - Seamless transition from AsyncStorage to SQLite
 * Handles data migration and fallback strategies
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import SQLiteService from './SQLiteService';

class MigrationService {
  constructor() {
    this.migrationCompleted = false;
    this.useSQLite = false;
  }

  async initialize() {
    try {
      // Check if migration has been completed before
      const migrationStatus = await AsyncStorage.getItem('sqlite_migration_completed');

      if (migrationStatus === 'true') {
        // Migration already completed, use SQLite
        await SQLiteService.initialize();
        this.useSQLite = true;
        this.migrationCompleted = true;
        console.log('Using SQLite database (migration previously completed)');
        return;
      }

      // Try to initialize SQLite
      await SQLiteService.initialize();

      // Migrate existing data from AsyncStorage
      await this.migrateData();

      // Mark migration as completed
      await AsyncStorage.setItem('sqlite_migration_completed', 'true');

      this.useSQLite = true;
      this.migrationCompleted = true;
      console.log('SQLite migration completed successfully');

    } catch (error) {
      console.warn('SQLite initialization failed, falling back to AsyncStorage:', error);
      // Fallback to AsyncStorage if SQLite fails
      this.useSQLite = false;
      this.migrationCompleted = false;
    }
  }

  async migrateData() {
    console.log('Starting data migration from AsyncStorage to SQLite...');

    try {
      // Get all existing data from AsyncStorage
      const [products, orders, customers, expenses, reconciliations] = await Promise.all([
        this.getAsyncStorageData('bakeryData'),
        this.getAsyncStorageData('orders'),
        this.getAsyncStorageData('customers'),
        this.getAsyncStorageData('financial_expenses'),
        this.getAsyncStorageData('cash_reconciliations')
      ]);

      const migrationData = {
        products: products?.products || [],
        orders: orders || [],
        customers: customers || [],
        expenses: expenses || [],
        reconciliations: reconciliations || []
      };

      // Only migrate if there's actual data
      const hasData = migrationData.products.length > 0 ||
                     migrationData.orders.length > 0 ||
                     migrationData.customers.length > 0;

      if (hasData) {
        await SQLiteService.migrateFromAsyncStorage(migrationData);
        console.log('Data migration completed:', {
          products: migrationData.products.length,
          orders: migrationData.orders.length,
          customers: migrationData.customers.length
        });
      } else {
        console.log('No existing data found to migrate');
      }

    } catch (error) {
      console.error('Migration failed:', error);
      throw error;
    }
  }

  async getAsyncStorageData(key) {
    try {
      const data = await AsyncStorage.getItem(key);
      return data ? JSON.parse(data) : null;
    } catch (error) {
      console.warn(`Failed to get AsyncStorage data for key ${key}:`, error);
      return null;
    }
  }

  // Unified data access methods that automatically choose the right storage
  async getProducts(filters = {}) {
    if (this.useSQLite) {
      return await SQLiteService.getProducts(filters);
    } else {
      // Fallback to AsyncStorage
      const data = await this.getAsyncStorageData('bakeryData');
      let products = data?.products || [];

      // Apply filters
      if (filters.isActive !== undefined) {
        products = products.filter(p => p.isActive === filters.isActive);
      }
      if (filters.category) {
        products = products.filter(p => p.category === filters.category);
      }
      if (filters.search) {
        const search = filters.search.toLowerCase();
        products = products.filter(p =>
          p.name.toLowerCase().includes(search) ||
          p.description?.toLowerCase().includes(search) ||
          p.sku?.toLowerCase().includes(search)
        );
      }

      return products;
    }
  }

  async saveProduct(product) {
    if (this.useSQLite) {
      return await SQLiteService.saveProduct(product);
    } else {
      // Fallback to AsyncStorage
      const data = await this.getAsyncStorageData('bakeryData') || { products: [] };
      const products = data.products || [];

      if (product.id) {
        const index = products.findIndex(p => p.id === product.id);
        if (index >= 0) {
          products[index] = { ...product, updatedAt: new Date().toISOString() };
        }
      } else {
        const newProduct = {
          ...product,
          id: Date.now(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        products.push(newProduct);
        product = newProduct;
      }

      await AsyncStorage.setItem('bakeryData', JSON.stringify({ ...data, products }));
      return product;
    }
  }

  async getOrders(filters = {}) {
    if (this.useSQLite) {
      return await SQLiteService.getOrders(filters);
    } else {
      // Fallback to AsyncStorage
      let orders = await this.getAsyncStorageData('orders') || [];

      // Apply filters
      if (filters.status) {
        orders = orders.filter(o => o.status === filters.status);
      }
      if (filters.dateFrom) {
        orders = orders.filter(o => o.date >= filters.dateFrom);
      }
      if (filters.dateTo) {
        orders = orders.filter(o => o.date <= filters.dateTo);
      }
      if (filters.search) {
        const search = filters.search.toLowerCase();
        orders = orders.filter(o =>
          o.customerName?.toLowerCase().includes(search) ||
          o.id?.toLowerCase().includes(search)
        );
      }

      return orders;
    }
  }

  async saveOrder(order) {
    if (this.useSQLite) {
      return await SQLiteService.saveOrder(order);
    } else {
      // Fallback to AsyncStorage
      const orders = await StorageService.get('orders') || [];

      if (order.id) {
        const index = orders.findIndex(o => o.id === order.id);
        if (index >= 0) {
          orders[index] = { ...order, updatedAt: new Date().toISOString() };
        } else {
          orders.push({ ...order, createdAt: new Date().toISOString(), updatedAt: new Date().toISOString() });
        }
      } else {
        const newOrder = {
          ...order,
          id: `ORD-${Date.now()}`,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        orders.push(newOrder);
        order = newOrder;
      }

      await StorageService.set('orders', orders);
      return order;
    }
  }

  async getCustomers() {
    if (this.useSQLite) {
      return await SQLiteService.getCustomers();
    } else {
      return await StorageService.get('customers') || [];
    }
  }

  async deleteProduct(id) {
    if (this.useSQLite) {
      return await SQLiteService.deleteProduct(id);
    } else {
      const data = await StorageService.get('bakeryData') || { products: [] };
      const products = data.products.filter(p => p.id !== id);
      await StorageService.set('bakeryData', { ...data, products });
    }
  }

  async deleteOrder(id) {
    if (this.useSQLite) {
      return await SQLiteService.deleteOrder(id);
    } else {
      const orders = await StorageService.get('orders') || [];
      const filteredOrders = orders.filter(o => o.id !== id);
      await StorageService.set('orders', filteredOrders);
    }
  }

  async deleteCustomer(id) {
    if (this.useSQLite) {
      return await SQLiteService.deleteCustomer(id);
    } else {
      const customers = await StorageService.get('customers') || [];
      const filteredCustomers = customers.filter(c => c.id !== id);
      await StorageService.set('customers', filteredCustomers);
    }
  }

  async saveCustomer(customer) {
    if (this.useSQLite) {
      return await SQLiteService.saveCustomer(customer);
    } else {
      const customers = await StorageService.get('customers') || [];

      if (customer.id) {
        const index = customers.findIndex(c => c.id === customer.id);
        if (index >= 0) {
          customers[index] = { ...customer, updatedAt: new Date().toISOString() };
        }
      } else {
        const newCustomer = {
          ...customer,
          id: Date.now(),
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        customers.push(newCustomer);
        customer = newCustomer;
      }

      await StorageService.set('customers', customers);
      return customer;
    }
  }

  // Performance analytics
  getPerformanceInfo() {
    return {
      usingDatabase: this.useSQLite ? 'SQLite' : 'AsyncStorage',
      migrationCompleted: this.migrationCompleted,
      optimizedForScale: this.useSQLite,
      recommendedMaxRecords: this.useSQLite ? 100000 : 5000,
      currentPerformanceLevel: this.useSQLite ? 'Excellent' : 'Good'
    };
  }

  // Health check
  async healthCheck() {
    try {
      if (this.useSQLite) {
        // Test SQLite connection
        await SQLiteService.db.getFirstAsync('SELECT 1 as test');
        return {
          status: 'healthy',
          database: 'SQLite',
          performance: 'Excellent',
          scalability: 'High'
        };
      } else {
        // Test AsyncStorage
        await AsyncStorage.getItem('test');
        return {
          status: 'healthy',
          database: 'AsyncStorage',
          performance: 'Good',
          scalability: 'Medium'
        };
      }
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        database: this.useSQLite ? 'SQLite' : 'AsyncStorage'
      };
    }
  }
}

export default new MigrationService();
