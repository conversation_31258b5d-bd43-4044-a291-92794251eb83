import AsyncStorage from '@react-native-async-storage/async-storage';
import { CacheEntry, CacheConfig } from '../types';
import LoggingService from './LoggingService';

interface CacheStats {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  evictions: number;
  hitRate: number;
}

class CacheService {
  private memoryCache = new Map<string, CacheEntry>();
  private accessOrder = new Map<string, number>(); // LRU tracking
  private cacheStats: CacheStats = {
    hits: 0,
    misses: 0,
    sets: 0,
    deletes: 0,
    evictions: 0,
    hitRate: 0,
  };
  
  private config: CacheConfig = {
    maxSize: 2000, // Increased for better performance
    defaultTTL: 7200000, // 2 hours for better retention
    cleanupInterval: 300000, // 5 minutes
  };
  
  private criticalKeys = new Set(['products', 'orders', 'customers', 'financial_data']);
  private preloadedKeys = new Set<string>();
  private cleanupTimer?: NodeJS.Timeout;
  private accessCounter = 0;

  constructor() {
    this.startCleanupInterval();
    this.initializePredictiveCache();
    LoggingService.info('CacheService initialized', 'CACHE', { config: this.config });
  }

  private startCleanupInterval(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.config.cleanupInterval);
  }

  private initializePredictiveCache(): void {
    // Preload critical data patterns
    this.preloadCriticalData();
    
    // Set up intelligent prefetching
    this.setupIntelligentPrefetching();
  }

  private async preloadCriticalData(): Promise<void> {
    try {
      const criticalData = await Promise.all([
        this.getPersistentCache('products'),
        this.getPersistentCache('orders'),
        this.getPersistentCache('customers'),
        this.getPersistentCache('financial_data'),
      ]);

      criticalData.forEach((data, index) => {
        const keys = Array.from(this.criticalKeys);
        if (data && keys[index]) {
          this.setMemoryCache(keys[index], data, this.config.defaultTTL * 2); // Longer TTL for critical data
          this.preloadedKeys.add(keys[index]);
        }
      });

      LoggingService.info('Critical data preloaded', 'CACHE', { 
        preloadedCount: this.preloadedKeys.size 
      });
    } catch (error) {
      LoggingService.error('Failed to preload critical data', 'CACHE', error);
    }
  }

  private setupIntelligentPrefetching(): void {
    // Prefetch related data based on access patterns
    const prefetchPatterns = {
      'products': ['product_categories', 'product_stats'],
      'orders': ['order_stats', 'recent_orders'],
      'customers': ['customer_stats', 'top_customers'],
      'dashboard': ['products', 'orders', 'customers', 'financial_data'],
    };

    Object.entries(prefetchPatterns).forEach(([key, relatedKeys]) => {
      if (this.memoryCache.has(key)) {
        relatedKeys.forEach(relatedKey => {
          if (!this.memoryCache.has(relatedKey)) {
            this.prefetchData(relatedKey);
          }
        });
      }
    });
  }

  private async prefetchData(key: string): Promise<void> {
    try {
      const data = await this.getPersistentCache(key);
      if (data) {
        this.setMemoryCache(key, data);
        LoggingService.debug(`Prefetched data for key: ${key}`, 'CACHE');
      }
    } catch (error) {
      LoggingService.debug(`Failed to prefetch data for key: ${key}`, 'CACHE', error);
    }
  }

  public setMemoryCache(key: string, data: any, ttl: number = this.config.defaultTTL): void {
    try {
      const now = Date.now();
      const entry: CacheEntry = {
        key,
        data,
        timestamp: now,
        ttl,
      };

      // Handle cache size limit with intelligent eviction
      if (this.memoryCache.size >= this.config.maxSize) {
        this.intelligentEviction();
      }

      this.memoryCache.set(key, entry);
      this.accessOrder.set(key, ++this.accessCounter);
      this.cacheStats.sets++;

      LoggingService.debug(`Cache set: ${key}`, 'CACHE', { ttl, size: this.memoryCache.size });
    } catch (error) {
      LoggingService.error(`Failed to set cache for key: ${key}`, 'CACHE', error);
    }
  }

  public getMemoryCache(key: string): any {
    try {
      const entry = this.memoryCache.get(key);
      
      if (!entry) {
        this.cacheStats.misses++;
        this.updateHitRate();
        return null;
      }

      const now = Date.now();
      if (now - entry.timestamp > entry.ttl) {
        this.memoryCache.delete(key);
        this.accessOrder.delete(key);
        this.cacheStats.misses++;
        this.updateHitRate();
        LoggingService.debug(`Cache expired: ${key}`, 'CACHE');
        return null;
      }

      // Update access order for LRU
      this.accessOrder.set(key, ++this.accessCounter);
      this.cacheStats.hits++;
      this.updateHitRate();

      LoggingService.debug(`Cache hit: ${key}`, 'CACHE');
      return entry.data;
    } catch (error) {
      LoggingService.error(`Failed to get cache for key: ${key}`, 'CACHE', error);
      this.cacheStats.misses++;
      this.updateHitRate();
      return null;
    }
  }

  private intelligentEviction(): void {
    // Never evict critical keys
    const evictableEntries = Array.from(this.memoryCache.entries())
      .filter(([key]) => !this.criticalKeys.has(key))
      .sort((a, b) => {
        const accessA = this.accessOrder.get(a[0]) || 0;
        const accessB = this.accessOrder.get(b[0]) || 0;
        return accessA - accessB; // Least recently used first
      });

    if (evictableEntries.length > 0) {
      const [keyToEvict] = evictableEntries[0];
      this.memoryCache.delete(keyToEvict);
      this.accessOrder.delete(keyToEvict);
      this.cacheStats.evictions++;
      LoggingService.debug(`Evicted cache entry: ${keyToEvict}`, 'CACHE');
    }
  }

  public async setPersistentCache(key: string, data: any, ttl: number = this.config.defaultTTL): Promise<void> {
    try {
      const entry: CacheEntry = {
        key,
        data,
        timestamp: Date.now(),
        ttl,
      };

      await AsyncStorage.setItem(`cache_${key}`, JSON.stringify(entry));
      
      // Also set in memory cache for immediate access
      this.setMemoryCache(key, data, ttl);
      
      LoggingService.debug(`Persistent cache set: ${key}`, 'CACHE');
    } catch (error) {
      LoggingService.error(`Failed to set persistent cache for key: ${key}`, 'CACHE', error);
    }
  }

  public async getPersistentCache(key: string): Promise<any> {
    try {
      const cached = await AsyncStorage.getItem(`cache_${key}`);
      
      if (!cached) {
        return null;
      }

      const entry: CacheEntry = JSON.parse(cached);
      const now = Date.now();

      if (now - entry.timestamp > entry.ttl) {
        await AsyncStorage.removeItem(`cache_${key}`);
        LoggingService.debug(`Persistent cache expired: ${key}`, 'CACHE');
        return null;
      }

      // Update memory cache with persistent data
      this.setMemoryCache(key, entry.data, entry.ttl - (now - entry.timestamp));
      
      return entry.data;
    } catch (error) {
      LoggingService.error(`Failed to get persistent cache for key: ${key}`, 'CACHE', error);
      return null;
    }
  }

  public async get(key: string): Promise<any> {
    // Try memory cache first (fastest)
    let data = this.getMemoryCache(key);
    if (data !== null) {
      return data;
    }

    // Fallback to persistent cache
    data = await this.getPersistentCache(key);
    return data;
  }

  public async set(key: string, data: any, ttl: number = this.config.defaultTTL): Promise<void> {
    // Set in both memory and persistent cache
    await Promise.all([
      this.setPersistentCache(key, data, ttl),
      // Memory cache is set within setPersistentCache
    ]);
  }

  public clearMemoryCache(): void {
    this.memoryCache.clear();
    this.accessOrder.clear();
    this.accessCounter = 0;
    LoggingService.info('Memory cache cleared', 'CACHE');
  }

  public async clearPersistentCache(): Promise<void> {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const cacheKeys = keys.filter(key => key.startsWith('cache_'));
      
      if (cacheKeys.length > 0) {
        await AsyncStorage.multiRemove(cacheKeys);
      }
      
      LoggingService.info('Persistent cache cleared', 'CACHE', { clearedKeys: cacheKeys.length });
    } catch (error) {
      LoggingService.error('Failed to clear persistent cache', 'CACHE', error);
    }
  }

  private cleanup(): void {
    const now = Date.now();
    let cleanedCount = 0;

    for (const [key, entry] of this.memoryCache.entries()) {
      if (now - entry.timestamp > entry.ttl && !this.criticalKeys.has(key)) {
        this.memoryCache.delete(key);
        this.accessOrder.delete(key);
        cleanedCount++;
      }
    }

    if (cleanedCount > 0) {
      LoggingService.debug(`Cache cleanup completed`, 'CACHE', { cleanedCount });
    }
  }

  private updateHitRate(): void {
    const total = this.cacheStats.hits + this.cacheStats.misses;
    this.cacheStats.hitRate = total > 0 ? this.cacheStats.hits / total : 0;
    
    // Update performance metrics
    LoggingService.updatePerformanceMetric('cacheHitRate', this.cacheStats.hitRate * 100);
  }

  public getStats(): CacheStats {
    return { ...this.cacheStats };
  }

  public resetStats(): void {
    this.cacheStats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      evictions: 0,
      hitRate: 0,
    };
    LoggingService.info('Cache stats reset', 'CACHE');
  }

  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }
    this.clearMemoryCache();
    LoggingService.info('CacheService destroyed', 'CACHE');
  }
}

export default new CacheService();
