/**
 * SQLite Database Service for Optimal Performance
 * Handles all database operations with proper indexing and optimization
 */

import * as SQLite from 'expo-sqlite';

class SQLiteService {
  constructor() {
    this.db = null;
    this.isInitialized = false;
  }

  async initialize() {
    if (this.isInitialized) return;

    try {
      this.db = await SQLite.openDatabaseAsync('bakery_app.db');
      await this.createTables();
      await this.createIndexes();
      this.isInitialized = true;
      console.log('SQLite database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize SQLite database:', error);
      throw error;
    }
  }

  async createTables() {
    const tables = [
      // Products table
      `CREATE TABLE IF NOT EXISTS products (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT,
        price REAL NOT NULL,
        cost REAL NOT NULL,
        stock INTEGER DEFAULT 0,
        sku TEXT UNIQUE,
        barcode TEXT,
        category TEXT,
        image TEXT,
        isActive INTEGER DEFAULT 1,
        isFeatured INTEGER DEFAULT 0,
        tags TEXT, -- J<PERSON><PERSON> string
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Orders table
      `CREATE TABLE IF NOT EXISTS orders (
        id TEXT PRIMARY KEY,
        customerName TEXT NOT NULL,
        customer TEXT,
        email TEXT,
        phone TEXT,
        date TEXT NOT NULL,
        time TEXT NOT NULL,
        status TEXT NOT NULL,
        orderType TEXT DEFAULT 'pickup',
        subtotal REAL NOT NULL,
        tax REAL DEFAULT 0,
        discount REAL DEFAULT 0,
        total REAL NOT NULL,
        notes TEXT,
        image TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Order items table
      `CREATE TABLE IF NOT EXISTS order_items (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        orderId TEXT NOT NULL,
        productId INTEGER NOT NULL,
        productName TEXT NOT NULL,
        quantity INTEGER NOT NULL,
        price REAL NOT NULL,
        total REAL NOT NULL,
        FOREIGN KEY (orderId) REFERENCES orders (id) ON DELETE CASCADE,
        FOREIGN KEY (productId) REFERENCES products (id)
      )`,

      // Customers table
      `CREATE TABLE IF NOT EXISTS customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT,
        phone TEXT,
        address TEXT,
        notes TEXT,
        isVIP INTEGER DEFAULT 0,
        totalOrders INTEGER DEFAULT 0,
        totalSpent REAL DEFAULT 0,
        tags TEXT, -- JSON string
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Financial expenses table
      `CREATE TABLE IF NOT EXISTS expenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category TEXT NOT NULL,
        amount REAL NOT NULL,
        description TEXT,
        date TEXT NOT NULL,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Cash reconciliations table
      `CREATE TABLE IF NOT EXISTS cash_reconciliations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        expectedCash REAL NOT NULL,
        actualCash REAL NOT NULL,
        difference REAL NOT NULL,
        notes TEXT,
        createdAt TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`,

      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL,
        updatedAt TEXT NOT NULL
      )`
    ];

    for (const table of tables) {
      await this.db.execAsync(table);
    }
  }

  async createIndexes() {
    const indexes = [
      // Product indexes
      'CREATE INDEX IF NOT EXISTS idx_products_name ON products (name)',
      'CREATE INDEX IF NOT EXISTS idx_products_category ON products (category)',
      'CREATE INDEX IF NOT EXISTS idx_products_sku ON products (sku)',
      'CREATE INDEX IF NOT EXISTS idx_products_barcode ON products (barcode)',
      'CREATE INDEX IF NOT EXISTS idx_products_active ON products (isActive)',
      'CREATE INDEX IF NOT EXISTS idx_products_featured ON products (isFeatured)',

      // Order indexes
      'CREATE INDEX IF NOT EXISTS idx_orders_date ON orders (date)',
      'CREATE INDEX IF NOT EXISTS idx_orders_status ON orders (status)',
      'CREATE INDEX IF NOT EXISTS idx_orders_customer ON orders (customerName)',
      'CREATE INDEX IF NOT EXISTS idx_orders_total ON orders (total)',
      'CREATE INDEX IF NOT EXISTS idx_orders_created ON orders (createdAt)',

      // Order items indexes
      'CREATE INDEX IF NOT EXISTS idx_order_items_order ON order_items (orderId)',
      'CREATE INDEX IF NOT EXISTS idx_order_items_product ON order_items (productId)',

      // Customer indexes
      'CREATE INDEX IF NOT EXISTS idx_customers_name ON customers (name)',
      'CREATE INDEX IF NOT EXISTS idx_customers_email ON customers (email)',
      'CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers (phone)',
      'CREATE INDEX IF NOT EXISTS idx_customers_vip ON customers (isVIP)',

      // Expense indexes
      'CREATE INDEX IF NOT EXISTS idx_expenses_category ON expenses (category)',
      'CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses (date)',

      // Cash reconciliation indexes
      'CREATE INDEX IF NOT EXISTS idx_cash_date ON cash_reconciliations (date)'
    ];

    for (const index of indexes) {
      await this.db.execAsync(index);
    }
  }

  // Product operations
  async getProducts(filters = {}) {
    let query = 'SELECT * FROM products WHERE 1=1';
    const params = [];

    if (filters.isActive !== undefined) {
      query += ' AND isActive = ?';
      params.push(filters.isActive ? 1 : 0);
    }

    if (filters.category) {
      query += ' AND category = ?';
      params.push(filters.category);
    }

    if (filters.search) {
      query += ' AND (name LIKE ? OR description LIKE ? OR sku LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY name ASC';

    const result = await this.db.getAllAsync(query, params);
    return result.map(this.parseProduct);
  }

  async getProductById(id) {
    const result = await this.db.getFirstAsync('SELECT * FROM products WHERE id = ?', [id]);
    return result ? this.parseProduct(result) : null;
  }

  async saveProduct(product) {
    const now = new Date().toISOString();

    if (product.id) {
      // Update existing product
      await this.db.runAsync(`
        UPDATE products SET
          name = ?, description = ?, price = ?, cost = ?, stock = ?,
          sku = ?, barcode = ?, category = ?, image = ?, isActive = ?,
          isFeatured = ?, tags = ?, updatedAt = ?
        WHERE id = ?
      `, [
        product.name, product.description, product.price, product.cost, product.stock,
        product.sku, product.barcode, product.category, product.image,
        product.isActive ? 1 : 0, product.isFeatured ? 1 : 0,
        JSON.stringify(product.tags || []), now, product.id
      ]);
      return { ...product, updatedAt: now };
    } else {
      // Insert new product
      const result = await this.db.runAsync(`
        INSERT INTO products (
          name, description, price, cost, stock, sku, barcode, category,
          image, isActive, isFeatured, tags, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        product.name, product.description, product.price, product.cost, product.stock,
        product.sku, product.barcode, product.category, product.image,
        product.isActive ? 1 : 0, product.isFeatured ? 1 : 0,
        JSON.stringify(product.tags || []), now, now
      ]);

      return { ...product, id: result.lastInsertRowId, createdAt: now, updatedAt: now };
    }
  }

  async deleteProduct(id) {
    await this.db.runAsync('DELETE FROM products WHERE id = ?', [id]);
  }

  // Order operations
  async getOrders(filters = {}) {
    let query = `
      SELECT o.*,
        GROUP_CONCAT(
          json_object(
            'productId', oi.productId,
            'productName', oi.productName,
            'quantity', oi.quantity,
            'price', oi.price,
            'total', oi.total
          )
        ) as items
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.orderId
      WHERE 1=1
    `;
    const params = [];

    if (filters.status) {
      query += ' AND o.status = ?';
      params.push(filters.status);
    }

    if (filters.dateFrom) {
      query += ' AND o.date >= ?';
      params.push(filters.dateFrom);
    }

    if (filters.dateTo) {
      query += ' AND o.date <= ?';
      params.push(filters.dateTo);
    }

    if (filters.search) {
      query += ' AND (o.customerName LIKE ? OR o.id LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm);
    }

    query += ' GROUP BY o.id ORDER BY o.createdAt DESC';

    const result = await this.db.getAllAsync(query, params);
    return result.map(this.parseOrder);
  }

  async getOrderById(id) {
    const query = `
      SELECT o.*,
        GROUP_CONCAT(
          json_object(
            'productId', oi.productId,
            'productName', oi.productName,
            'quantity', oi.quantity,
            'price', oi.price,
            'total', oi.total
          )
        ) as items
      FROM orders o
      LEFT JOIN order_items oi ON o.id = oi.orderId
      WHERE o.id = ?
      GROUP BY o.id
    `;

    const result = await this.db.getFirstAsync(query, [id]);
    return result ? this.parseOrder(result) : null;
  }

  async saveOrder(order) {
    const now = new Date().toISOString();

    await this.db.withTransactionAsync(async () => {
      if (order.id && await this.getOrderById(order.id)) {
        // Update existing order
        await this.db.runAsync(`
          UPDATE orders SET
            customerName = ?, customer = ?, email = ?, phone = ?, date = ?, time = ?,
            status = ?, orderType = ?, subtotal = ?, tax = ?, discount = ?, total = ?,
            notes = ?, image = ?, updatedAt = ?
          WHERE id = ?
        `, [
          order.customerName, order.customer, order.email, order.phone,
          order.date, order.time, order.status, order.orderType,
          order.subtotal, order.tax, order.discount, order.total,
          order.notes, order.image, now, order.id
        ]);

        // Delete existing order items
        await this.db.runAsync('DELETE FROM order_items WHERE orderId = ?', [order.id]);
      } else {
        // Insert new order
        await this.db.runAsync(`
          INSERT INTO orders (
            id, customerName, customer, email, phone, date, time, status,
            orderType, subtotal, tax, discount, total, notes, image, createdAt, updatedAt
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          order.id, order.customerName, order.customer, order.email, order.phone,
          order.date, order.time, order.status, order.orderType,
          order.subtotal, order.tax, order.discount, order.total,
          order.notes, order.image, now, now
        ]);
      }

      // Insert order items
      if (order.items && order.items.length > 0) {
        for (const item of order.items) {
          await this.db.runAsync(`
            INSERT INTO order_items (orderId, productId, productName, quantity, price, total)
            VALUES (?, ?, ?, ?, ?, ?)
          `, [order.id, item.productId, item.productName, item.quantity, item.price, item.total]);
        }
      }
    });

    return { ...order, updatedAt: now };
  }

  async deleteOrder(id) {
    await this.db.withTransactionAsync(async () => {
      // Delete order items first (foreign key constraint)
      await this.db.runAsync('DELETE FROM order_items WHERE orderId = ?', [id]);
      // Delete the order
      await this.db.runAsync('DELETE FROM orders WHERE id = ?', [id]);
    });
  }

  async deleteCustomer(id) {
    await this.db.runAsync('DELETE FROM customers WHERE id = ?', [id]);
  }

  // Helper methods
  parseProduct(row) {
    return {
      ...row,
      isActive: Boolean(row.isActive),
      isFeatured: Boolean(row.isFeatured),
      tags: row.tags ? JSON.parse(row.tags) : []
    };
  }

  parseOrder(row) {
    return {
      ...row,
      items: row.items ? row.items.split(',').map(item => JSON.parse(item)) : []
    };
  }

  // Migration method to import existing data
  async migrateFromAsyncStorage(data) {
    console.log('Starting SQLite migration...');

    await this.db.withTransactionAsync(async () => {
      // Migrate products
      if (data.products) {
        for (const product of data.products) {
          await this.saveProduct(product);
        }
        console.log(`Migrated ${data.products.length} products`);
      }

      // Migrate orders
      if (data.orders) {
        for (const order of data.orders) {
          await this.saveOrder(order);
        }
        console.log(`Migrated ${data.orders.length} orders`);
      }

      // Migrate customers
      if (data.customers) {
        for (const customer of data.customers) {
          await this.saveCustomer(customer);
        }
        console.log(`Migrated ${data.customers.length} customers`);
      }
    });

    console.log('SQLite migration completed successfully');
  }

  async saveCustomer(customer) {
    const now = new Date().toISOString();

    if (customer.id) {
      await this.db.runAsync(`
        UPDATE customers SET
          name = ?, email = ?, phone = ?, address = ?, notes = ?,
          isVIP = ?, totalOrders = ?, totalSpent = ?, tags = ?, updatedAt = ?
        WHERE id = ?
      `, [
        customer.name, customer.email, customer.phone, customer.address, customer.notes,
        customer.isVIP ? 1 : 0, customer.totalOrders, customer.totalSpent,
        JSON.stringify(customer.tags || []), now, customer.id
      ]);
    } else {
      const result = await this.db.runAsync(`
        INSERT INTO customers (
          name, email, phone, address, notes, isVIP, totalOrders, totalSpent, tags, createdAt, updatedAt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        customer.name, customer.email, customer.phone, customer.address, customer.notes,
        customer.isVIP ? 1 : 0, customer.totalOrders, customer.totalSpent,
        JSON.stringify(customer.tags || []), now, now
      ]);

      return { ...customer, id: result.lastInsertRowId, createdAt: now, updatedAt: now };
    }
  }

  async getCustomers() {
    const result = await this.db.getAllAsync('SELECT * FROM customers ORDER BY name ASC');
    return result.map(row => ({
      ...row,
      isVIP: Boolean(row.isVIP),
      tags: row.tags ? JSON.parse(row.tags) : []
    }));
  }
}

export default new SQLiteService();
