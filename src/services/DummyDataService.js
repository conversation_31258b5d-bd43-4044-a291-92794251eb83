/**
 * DummyDataService - Generates and manages dummy data for testing
 *
 * @description Generates realistic dummy data once and saves to SQLite.
 * Includes 1000 orders, 20 products, and 900 customers.
 * Data is generated only once and cached for performance.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import SQLiteService from './SQLiteService';

class DummyDataService {
  constructor() {
    this.isInitialized = false;
    this.DUMMY_DATA_KEY = 'dummy_data_generated';
  }

  /**
   * Check if dummy data has already been generated
   */
  async isDummyDataGenerated() {
    try {
      const generated = await AsyncStorage.getItem(this.DUMMY_DATA_KEY);
      return generated === 'true';
    } catch (error) {
      console.error('Error checking dummy data status:', error);
      return false;
    }
  }

  /**
   * Mark dummy data as generated
   */
  async markDummyDataGenerated() {
    try {
      await AsyncStorage.setItem(this.DUMMY_DATA_KEY, 'true');
    } catch (error) {
      console.error('Error marking dummy data as generated:', error);
    }
  }

  /**
   * Generate realistic product names and categories
   */
  generateProducts() {
    const categories = ['Cakes', 'Pastries', 'Bread', 'Cookies', 'Beverages', 'Desserts', 'Seasonal'];
    const productNames = {
      'Cakes': ['Chocolate Cake', 'Vanilla Sponge', 'Red Velvet', 'Carrot Cake'],
      'Pastries': ['Croissant', 'Danish Pastry', 'Eclair', 'Profiterole'],
      'Bread': ['Sourdough', 'Baguette', 'Whole Wheat', 'Rye Bread'],
      'Cookies': ['Chocolate Chip', 'Oatmeal Raisin', 'Sugar Cookie', 'Gingerbread'],
      'Beverages': ['Coffee', 'Tea', 'Hot Chocolate', 'Fresh Juice'],
      'Desserts': ['Tiramisu', 'Cheesecake', 'Apple Pie', 'Ice Cream'],
      'Seasonal': ['Christmas Cake', 'Easter Bread', 'Halloween Cookies', 'Valentine Treats']
    };

    const products = [];
    let productId = 1;

    categories.forEach(category => {
      const names = productNames[category];
      names.forEach(name => {
        if (products.length < 20) {
          products.push({
            id: productId++,
            name: name,
            description: `Delicious ${name.toLowerCase()} made fresh daily`,
            price: Math.floor(Math.random() * 500) + 50, // 50-550 BDT
            cost: Math.floor(Math.random() * 200) + 20, // 20-220 BDT
            category: category,
            stock: Math.floor(Math.random() * 100) + 10, // 10-110 units
            sku: `PRD-${category.substring(0, 1)}-${String(productId).padStart(6, '0')}`,
            barcode: `${Math.floor(Math.random() * 9000000000000) + 1000000000000}`,
            image: null,
            isActive: Math.random() > 0.1, // 90% active
            isFeatured: Math.random() > 0.7, // 30% featured
            tags: [],
            createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString(),
            updatedAt: new Date().toISOString()
          });
        }
      });
    });

    return products;
  }

  /**
   * Generate realistic customer data
   */
  generateCustomers() {
    const firstNames = ['Ahmed', 'Fatima', 'Mohammad', 'Ayesha', 'Rahman', 'Khadija', 'Ali', 'Zainab', 'Omar', 'Maryam'];
    const lastNames = ['Khan', 'Rahman', 'Ahmed', 'Ali', 'Hassan', 'Hussain', 'Sheikh', 'Chowdhury', 'Islam', 'Begum'];
    const areas = ['Dhanmondi', 'Gulshan', 'Banani', 'Uttara', 'Mirpur', 'Wari', 'Old Dhaka', 'Mohammadpur'];

    const customers = [];

    for (let i = 1; i <= 900; i++) {
      const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
      const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
      const area = areas[Math.floor(Math.random() * areas.length)];

      customers.push({
        id: i,
        name: `${firstName} ${lastName}`,
        email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}${i}@email.com`,
        phone: `+880${Math.floor(Math.random() * 900000000) + 100000000}`,
        address: `House ${Math.floor(Math.random() * 200) + 1}, Road ${Math.floor(Math.random() * 50) + 1}, ${area}, Dhaka`,
        totalOrders: Math.floor(Math.random() * 20),
        totalSpent: Math.floor(Math.random() * 10000) + 500,
        createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000).toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    return customers;
  }

  /**
   * Generate realistic order data
   */
  generateOrders(products, customers) {
    const statuses = ['pending', 'confirmed', 'preparing', 'ready', 'delivered', 'cancelled'];
    const statusWeights = [0.1, 0.15, 0.1, 0.05, 0.55, 0.05]; // Most orders are delivered

    const orders = [];

    for (let i = 1; i <= 1000; i++) {
      const customer = customers[Math.floor(Math.random() * customers.length)];
      const orderDate = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000); // Last 90 days

      // Generate random status based on weights
      let randomValue = Math.random();
      let statusIndex = 0;
      let cumulativeWeight = 0;

      for (let j = 0; j < statusWeights.length; j++) {
        cumulativeWeight += statusWeights[j];
        if (randomValue <= cumulativeWeight) {
          statusIndex = j;
          break;
        }
      }

      const status = statuses[statusIndex];
      const itemCount = Math.floor(Math.random() * 5) + 1; // 1-5 items per order
      let totalAmount = 0;
      const orderItems = [];

      // Generate order items with proper structure
      for (let j = 0; j < itemCount; j++) {
        const product = products[Math.floor(Math.random() * products.length)];
        const quantity = Math.floor(Math.random() * 3) + 1; // 1-3 quantity
        const itemTotal = product.price * quantity;

        orderItems.push({
          productId: product.id,
          productName: product.name,
          price: product.price,
          quantity: quantity,
          total: itemTotal
        });

        totalAmount += itemTotal;
      }

      orders.push({
        id: `ORD-${String(i).padStart(6, '0')}`,
        customerName: customer.name,
        customer: customer.name,
        email: customer.email,
        phone: customer.phone,
        date: orderDate.toISOString().split('T')[0], // YYYY-MM-DD format
        time: orderDate.toTimeString().split(' ')[0], // HH:MM:SS format
        status: status,
        orderType: Math.random() > 0.5 ? 'pickup' : 'delivery',
        items: orderItems, // Add the items array
        subtotal: totalAmount,
        tax: Math.round(totalAmount * 0.05), // 5% tax
        discount: Math.random() > 0.8 ? Math.round(totalAmount * 0.1) : 0, // 10% discount sometimes
        total: totalAmount + Math.round(totalAmount * 0.05), // subtotal + tax
        notes: Math.random() > 0.7 ? 'Special instructions for this order' : '',
        image: null,
        createdAt: orderDate.toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    return orders.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt)); // Sort by date desc
  }

  /**
   * Initialize dummy data if not already generated
   */
  async initializeDummyData() {
    try {
      // Always check if data exists in database first
      const [products, orders, customers] = await Promise.all([
        SQLiteService.getProducts(),
        SQLiteService.getOrders(),
        SQLiteService.getCustomers()
      ]);

      console.log(`Current data in database: ${products.length} products, ${orders.length} orders, ${customers.length} customers`);

      // If we have sufficient data, skip generation
      if (products.length >= 20 && orders.length >= 1000 && customers.length >= 900) {
        console.log('Sufficient dummy data already exists, skipping generation');
        return { success: true, message: 'Dummy data already exists' };
      }

      // If we have partial data (orders but no products/customers), add minimal test data
      if (orders.length > 0 && (products.length === 0 || customers.length === 0)) {
        console.log('Partial data detected, adding minimal test data...');
        await this.addMinimalTestData();
        return { success: true, message: 'Minimal test data added successfully' };
      }

      // Check if generation was attempted but failed
      const alreadyGenerated = await this.isDummyDataGenerated();
      if (alreadyGenerated && (products.length === 0 && orders.length === 0 && customers.length === 0)) {
        console.log('Previous generation failed, resetting and regenerating...');
        await this.resetDummyData();
      }

      console.log('Generating dummy data...');

      // Generate data
      const newProducts = this.generateProducts();
      const newCustomers = this.generateCustomers();
      const newOrders = this.generateOrders(newProducts, newCustomers);

      console.log(`Generated: ${newProducts.length} products, ${newCustomers.length} customers, ${newOrders.length} orders`);

      // Save to SQLite using transactions for better performance
      await SQLiteService.initialize();

      // Use transactions to batch insert data efficiently
      await SQLiteService.db.withTransactionAsync(async () => {
        // Save products
        for (const product of newProducts) {
          await SQLiteService.saveProduct(product);
        }

        // Save customers
        for (const customer of newCustomers) {
          await SQLiteService.saveCustomer(customer);
        }

        // Save orders
        for (const order of newOrders) {
          await SQLiteService.saveOrder(order);
        }
      });

      // Mark as generated
      await this.markDummyDataGenerated();

      console.log('Dummy data generation completed successfully');

      return {
        success: true,
        message: 'Dummy data generated successfully',
        counts: {
          products: newProducts.length,
          customers: newCustomers.length,
          orders: newOrders.length
        }
      };

    } catch (error) {
      console.error('Error generating dummy data:', error);
      return {
        success: false,
        message: 'Failed to generate dummy data',
        error: error.message
      };
    }
  }

  /**
   * Add minimal test data without clearing existing orders
   */
  async addMinimalTestData() {
    try {
      console.log('Adding minimal test data...');

      // Generate minimal test data
      const testProducts = [
        {
          id: 'test-product-1',
          name: 'Test Product 1',
          price: 100,
          cost: 50,
          stock: 10,
          category: 'Test Category',
          sku: 'TEST001',
          barcode: '1234567890123',
          description: 'Test product for bulk operations',
          image: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'test-product-2',
          name: 'Test Product 2',
          price: 200,
          cost: 100,
          stock: 20,
          category: 'Test Category',
          sku: 'TEST002',
          barcode: '1234567890124',
          description: 'Another test product for bulk operations',
          image: null,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      const testCustomers = [
        {
          id: 'test-customer-1',
          name: 'Test Customer 1',
          email: '<EMAIL>',
          phone: '+1234567890',
          address: '123 Test Street',
          city: 'Test City',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        },
        {
          id: 'test-customer-2',
          name: 'Test Customer 2',
          email: '<EMAIL>',
          phone: '+1234567891',
          address: '456 Test Avenue',
          city: 'Test City',
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        }
      ];

      // Save test data to SQLite
      await SQLiteService.initialize();

      // Save products and customers
      for (const product of testProducts) {
        await SQLiteService.saveProduct(product);
      }

      for (const customer of testCustomers) {
        await SQLiteService.saveCustomer(customer);
      }

      console.log(`Added ${testProducts.length} test products and ${testCustomers.length} test customers`);
      return { success: true, counts: { products: testProducts.length, customers: testCustomers.length } };
    } catch (error) {
      console.error('Error adding minimal test data:', error);
      return { success: false, message: 'Failed to add test data' };
    }
  }

  /**
   * Clear all data from SQLite (for regeneration)
   */
  async clearAllData() {
    try {
      console.log('Clearing all data from SQLite...');

      // Use a transaction to ensure atomicity and prevent locks
      await SQLiteService.db.withTransactionAsync(async () => {
        await SQLiteService.db.execAsync('DELETE FROM order_items');
        await SQLiteService.db.execAsync('DELETE FROM orders');
        await SQLiteService.db.execAsync('DELETE FROM products');
        await SQLiteService.db.execAsync('DELETE FROM customers');
      });

      // Reset the dummy data flag
      await AsyncStorage.removeItem(this.DUMMY_DATA_KEY);

      console.log('All data cleared successfully');
      return { success: true, message: 'All data cleared successfully' };
    } catch (error) {
      console.error('Error clearing data:', error);
      return { success: false, message: 'Failed to clear data' };
    }
  }

  /**
   * Reset dummy data (for testing purposes)
   */
  async resetDummyData() {
    try {
      await AsyncStorage.removeItem(this.DUMMY_DATA_KEY);
      console.log('Dummy data reset - will regenerate on next initialization');
      return { success: true, message: 'Dummy data reset successfully' };
    } catch (error) {
      console.error('Error resetting dummy data:', error);
      return { success: false, message: 'Failed to reset dummy data' };
    }
  }
}

// Create singleton instance
const dummyDataService = new DummyDataService();

export default dummyDataService;
