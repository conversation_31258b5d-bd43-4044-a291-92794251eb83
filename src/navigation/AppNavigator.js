import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import { useTheme } from '../context/ThemeContext';

// Import navigators and screens
import TabNavigator from './TabNavigator';
import ProductsScreen from '../screens/ProductsScreen';
import CustomersScreen from '../screens/CustomersScreen';
import SearchScreen from '../screens/SearchScreen';
import MyProfileScreen from '../screens/ProfileScreen';
import AddProductScreen from '../screens/AddProductScreen';
import AddOrderScreen from '../screens/AddOrderScreen';
import AddCustomerScreen from '../screens/AddCustomerScreen';
import PaymentMethodsScreen from '../screens/PaymentMethodsScreen';
import EditProfileScreen from '../screens/EditProfileScreen';
import SecuritySettingsScreen from '../screens/SecuritySettingsScreen';
import ImportDataScreen from '../screens/ImportDataScreen';
import ActivityLogScreen from '../screens/ActivityLogScreen';
import ReportsScreen from '../screens/ReportsScreen';

const Stack = createStackNavigator();



const AppNavigator = () => {
  const { theme } = useTheme();

  return (
    <Stack.Navigator
      screenOptions={{
        headerShown: false,
        cardStyle: { backgroundColor: theme.colors.background },
        // Unified transition animations
        cardStyleInterpolator: ({ current, layouts }) => {
          return {
            cardStyle: {
              transform: [
                {
                  translateX: current.progress.interpolate({
                    inputRange: [0, 1],
                    outputRange: [layouts.screen.width, 0],
                  }),
                },
              ],
            },
          };
        },
      }}
      initialRouteName="Main"
    >
      {/* Main Tab Navigator */}
      <Stack.Screen
        name="Main"
        component={TabNavigator}
        options={{
          headerShown: false,
        }}
      />

      {/* Modal Screens */}
      <Stack.Screen
        name="Products"
        component={ProductsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Customers Screen */}
      <Stack.Screen
        name="Customers"
        component={CustomersScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Search Screen */}
      <Stack.Screen
        name="Search"
        component={SearchScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* My Profile Screen */}
      <Stack.Screen
        name="MyProfile"
        component={MyProfileScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Add Product Screen */}
      <Stack.Screen
        name="AddProduct"
        component={AddProductScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Add Order Screen */}
      <Stack.Screen
        name="AddOrder"
        component={AddOrderScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Add Customer Screen */}
      <Stack.Screen
        name="AddCustomer"
        component={AddCustomerScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Payment Methods Screen */}
      <Stack.Screen
        name="PaymentMethods"
        component={PaymentMethodsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Edit Profile Screen */}
      <Stack.Screen
        name="EditProfile"
        component={EditProfileScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Security Settings Screen */}
      <Stack.Screen
        name="SecuritySettings"
        component={SecuritySettingsScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Import Data Screen */}
      <Stack.Screen
        name="ImportData"
        component={ImportDataScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Activity Log Screen */}
      <Stack.Screen
        name="ActivityLog"
        component={ActivityLogScreen}
        options={{
          headerShown: false,
        }}
      />

      {/* Reports Screen */}
      <Stack.Screen
        name="Reports"
        component={ReportsScreen}
        options={{
          headerShown: false,
        }}
      />
    </Stack.Navigator>
  );
};

export default AppNavigator;
