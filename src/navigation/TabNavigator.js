import React, { useRef } from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { useNavigation } from '@react-navigation/native';
import { useTheme } from '../context/ThemeContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Import screens
import DashboardScreen from '../screens/DashboardScreen';
import ScanScreen from '../screens/ScanScreen';
import OrdersScreen from '../screens/OrdersScreen';
import SettingsScreen from '../screens/SettingsScreen';

// Import custom bottom nav bar and quick actions
import BottomNavBar from '../components/BottomNavBar';
import QuickActionsBottomSheet from '../components/QuickActionsBottomSheet';

// Import navigation service
import navigationService from '../services/NavigationService';

const Tab = createBottomTabNavigator();

const TabNavigator = () => {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const navigation = useNavigation();
  const quickActionsRef = useRef(null);

  const handlePlusPress = () => {
    console.log('Plus button pressed - opening quick actions');
    quickActionsRef.current?.open();
  };

  const handleQuickAction = (actionId) => {
    console.log('Quick action selected:', actionId);

    // Close the quick actions sheet first
    quickActionsRef.current?.close();

    // Add a small delay to ensure the bottom sheet closes before navigation
    setTimeout(() => {
      // Handle different quick actions
      switch (actionId) {
        case 'add-product':
          // Navigate to Add Product screen using NavigationService
          console.log('Navigating to AddProduct...');
          try {
            navigationService.navigate('AddProduct');
            console.log('Navigation to AddProduct successful');
          } catch (error) {
            console.error('Navigation to AddProduct failed:', error);
          }
          break;
        case 'add-order':
          // Navigate to Add Order screen using NavigationService
          console.log('Navigating to AddOrder...');
          try {
            navigationService.navigate('AddOrder');
            console.log('Navigation to AddOrder successful');
          } catch (error) {
            console.error('Navigation to AddOrder failed:', error);
          }
          break;
        case 'scan':
          // Navigate to Scan tab
          navigation.navigate('Scan');
          break;
        case 'add-customer':
          // Navigate to Dashboard tab
          navigation.navigate('Dashboard');
          break;
        default:
          console.log('Unknown action:', actionId);
      }
    }, 300); // 300ms delay
  };

  return (
    <>
      <Tab.Navigator
        screenOptions={{
          headerShown: false,
          tabBarStyle: { display: 'none' }, // Hide default tab bar
          // Unified screen transitions
          animationEnabled: true,
          animationTypeForReplace: 'push',
        }}
        tabBar={(props) => (
          <BottomNavBar
            {...props}
            currentRoute={props.state.routes[props.state.index].name}
            onPlusPress={handlePlusPress}
          />
        )}
      >
        <Tab.Screen
          name="Dashboard"
          component={DashboardScreen}
          options={{
            tabBarLabel: 'Dashboard',
          }}
        />
        <Tab.Screen
          name="Scan"
          component={ScanScreen}
          options={{
            tabBarLabel: 'Scan',
          }}
        />
        <Tab.Screen
          name="Orders"
          component={OrdersScreen}
          options={{
            tabBarLabel: 'Orders',
          }}
        />
        <Tab.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            tabBarLabel: 'Settings',
          }}
        />
      </Tab.Navigator>

      {/* Global Quick Actions Bottom Sheet */}
      <QuickActionsBottomSheet
        ref={quickActionsRef}
        onActionPress={handleQuickAction}
      />
    </>
  );
};

export default TabNavigator;
