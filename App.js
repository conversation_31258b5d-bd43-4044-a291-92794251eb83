import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { PaperProvider } from 'react-native-paper';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { GestureHandlerRootView } from 'react-native-gesture-handler';

// Import navigation and contexts
import AppNavigator from './src/navigation/AppNavigator';
import { DataProvider } from './src/context/DataContext';
import { ThemeProvider, useTheme } from './src/context/ThemeContext';
import { FinancialProvider } from './src/context/FinancialContext';
import { NavigationProvider } from './src/context/NavigationContext';
import { TabNavigationProvider } from './src/context/TabNavigationContext';
import BottomSheetProvider from './src/components/BottomSheetProvider';
import ErrorBoundary from './src/components/ErrorBoundary';
import { navigationRef } from './src/services/NavigationService';


// Main App Component that uses theme
const AppContent = () => {
  const { theme, isDarkMode } = useTheme();

  return (
    <PaperProvider theme={theme}>
      <NavigationContainer ref={navigationRef}>
        <BottomSheetProvider>
          <AppNavigator />
        </BottomSheetProvider>
        <StatusBar style={isDarkMode ? "light" : "dark"} />
      </NavigationContainer>
    </PaperProvider>
  );
};

// Root App Component with providers and error boundary
export default function App() {
  const handleAppError = () => {
    console.log('App error handled');
  };

  const handleAppReload = () => {
    console.log('App reload requested');
  };

  return (
    <ErrorBoundary
      onRetry={handleAppError}
      onReload={handleAppReload}
    >
      <GestureHandlerRootView style={{ flex: 1 }}>
        <SafeAreaProvider>
          <ErrorBoundary>
            <ThemeProvider>
              <ErrorBoundary>
                <DataProvider>
                  <ErrorBoundary>
                    <FinancialProvider>
                      <TabNavigationProvider>
                        <AppContent />
                      </TabNavigationProvider>
                    </FinancialProvider>
                  </ErrorBoundary>
                </DataProvider>
              </ErrorBoundary>
            </ThemeProvider>
          </ErrorBoundary>
        </SafeAreaProvider>
      </GestureHandlerRootView>
    </ErrorBoundary>
  );
}
